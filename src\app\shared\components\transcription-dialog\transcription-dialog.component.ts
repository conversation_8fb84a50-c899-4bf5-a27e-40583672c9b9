import { Component, Inject, OnInit } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialog,
} from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import {
  TranscriptionService,
  TranscriptionRequest,
  TranscriptionResponse,
} from '../../../services/transcription.service';
import {
  GoogleDriveFile,
  GoogleDriveService,
} from '../../../services/google-drive.service';
import {
  TranscriptionSaveService,
  TranscriptionSaveRequest,
  TranscriptionVerificationResponse,
} from '../../../services/transcription-save.service';
import { GenericResponse } from '@app/models/backend/generic-response';
import { ClienteResidencial } from '@app/models/backend/clienteresidencial';
import { GoogleDriveExplorerComponent } from '../google-drive-explorer/google-drive-explorer.component';
import { environment } from '@src/environments/environment';

export interface TranscriptionDialogData {
  file?: GoogleDriveFile;
  cliente?: {
    nombres: string;
    apellidos: string;
  };
  numeroMovil?: string;
  allowFileUpload?: boolean;
  fechaIngresado?: string; // Nueva propiedad para permitir subida de archivos
  // Información adicional del lead
  leadId?: string; // ID real del lead de la API
  dniCliente?: string; // DNI del cliente para las consultas
  fechaCreacion?: string; // Fecha de creación del lead
  nombreAsesor?: string; // Nombre del asesor
  numeroAgente?: string; // Número del agente
  transcripcionExistente?: {
    transcription: string;
    call_id: string;
    mood: string;
    mood_confidence: number;
    sentiment_score: number;
    call_duration: number;
    processing_time: number;
    audio_quality_score: number;
    word_count: number;
    language_detected: string;
    model_used: string;
    device_used: string;
    file_size_mb: number;
    created_at: string;
    call_datetime: string;
    urlDriveTranscripcion?: string;
    notaAgenteComparadorIA?: string;
    datosLeadCompletos?: any;
  };
}

@Component({
  selector: 'app-transcription-dialog',
  templateUrl: './transcription-dialog.component.html',
})
export class TranscriptionDialogComponent implements OnInit {
  // Estado del proceso
  isProcessing = false;
  currentStep:
    | 'config'
    | 'downloading'
    | 'transcribing'
    | 'completed'
    | 'error' = 'config';
  progress = 0;
  statusMessage = '';

  // Estado de verificación de transcripción
  isVerifying = false;
  verificationResult: TranscriptionVerificationResponse | null = null;
  showExistingTranscriptionWarning = false;

  // Estado de subida de archivos
  uploadedFile: File | null = null;
  isDragOver = false;
  allowFileUpload = false;

  // Configuración de transcripción
  transcriptionConfig = {
    whisper_model: 'base' as
      | 'tiny'
      | 'base'
      | 'small'
      | 'medium'
      | 'large'
      | 'turbo',
    device: 'cpu' as 'cpu' | 'gpu',
    target_language: 'es',
    call_type: 'inbound' as 'inbound' | 'outbound' | 'internal',
    agent_id: '',
    call_datetime: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:mm
  };

  // Resultado de la transcripción
  transcriptionResult: TranscriptionResponse | null = null;

  // Propiedades para mostrar información adicional
  showAdvancedInfo = false;

  // Opciones disponibles
  whisperModels = [
    {
      value: 'tiny',
      label: 'Tiny - Muy rápido, precisión básica (~39MB)',
      speed: '⚡⚡⚡',
      accuracy: '⭐',
    },
    {
      value: 'base',
      label: 'Base - Balanceado, buena precisión (~74MB) ⭐ Recomendado',
      speed: '⚡⚡',
      accuracy: '⭐⭐',
    },
    {
      value: 'small',
      label: 'Small - Buena calidad (~244MB)',
      speed: '⚡',
      accuracy: '⭐⭐⭐',
    },
    {
      value: 'medium',
      label: 'Medium - Alta precisión (~769MB)',
      speed: '⚡',
      accuracy: '⭐⭐⭐⭐',
    },
    {
      value: 'large',
      label: 'Large - Máxima precisión (~1550MB)',
      speed: '⚡',
      accuracy: '⭐⭐⭐⭐⭐',
    },
    {
      value: 'turbo',
      label: 'Turbo - Rápido y preciso (~809MB) ⭐ Mejor balance',
      speed: '⚡⚡⚡',
      accuracy: '⭐⭐⭐⭐',
    },
  ];

  devices = [
    { value: 'cpu', label: 'CPU - Compatibilidad total, más lento' },
    { value: 'gpu', label: 'GPU - Más rápido (requiere GPU NVIDIA con CUDA)' },
  ];

  callTypes = [
    { value: 'inbound', label: 'Entrante - Llamada recibida' },
    { value: 'outbound', label: 'Saliente - Llamada realizada' },
    { value: 'internal', label: 'Interna - Llamada entre agentes' },
  ];

  constructor(
    public dialogRef: MatDialogRef<TranscriptionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TranscriptionDialogData,
    private transcriptionService: TranscriptionService,
    private googleDriveService: GoogleDriveService,
    private transcriptionSaveService: TranscriptionSaveService,
    private snackBar: MatSnackBar,
    private http: HttpClient,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    // Configurar modo de subida de archivos
    this.allowFileUpload = this.data.allowFileUpload || false;

    // Verificar si se pasaron datos de transcripción existente
    if (this.data.transcripcionExistente) {
      this.cargarTranscripcionExistente(this.data.transcripcionExistente);
      return; // No continuar con el flujo normal
    }

    // Configurar valores iniciales basados en los datos del cliente
    if (this.data.numeroMovil) {
      this.transcriptionConfig.agent_id = this.data.numeroMovil;

      // Siempre usar el verificador local que es más eficiente
      this.verificarTranscripcionExistente();
    }
  }

  /**
   * Carga los datos de transcripción existente directamente en el componente
   */
  private cargarTranscripcionExistente(transcripcionExistente: any): void {
    // Configurar el resultado de transcripción con los datos existentes
    this.transcriptionResult = {
      success: true, // Marcar como exitoso
      call_id: transcripcionExistente.call_id,
      transcription: transcripcionExistente.transcription,
      mood: transcripcionExistente.mood,
      mood_confidence: transcripcionExistente.mood_confidence,
      sentiment_score: transcripcionExistente.sentiment_score,
      call_duration: transcripcionExistente.call_duration,
      processing_time: transcripcionExistente.processing_time,
      audio_quality_score: transcripcionExistente.audio_quality_score,
      word_count: transcripcionExistente.word_count,
      language_detected: transcripcionExistente.language_detected,
      model_used: transcripcionExistente.model_used,
      device_used: transcripcionExistente.device_used,
      file_size_mb: transcripcionExistente.file_size_mb,
      created_at: transcripcionExistente.created_at,
      call_datetime: transcripcionExistente.call_datetime,
    };

    // Agregar propiedades adicionales
    (this.transcriptionResult as any).urlDriveTranscripcion =
      transcripcionExistente.urlDriveTranscripcion;
    (this.transcriptionResult as any).notaAgenteComparadorIA =
      transcripcionExistente.notaAgenteComparadorIA;
    (this.transcriptionResult as any).datosLeadCompletos =
      transcripcionExistente.datosLeadCompletos;

    // Cambiar al estado de completado
    this.currentStep = 'completed';
    this.isProcessing = false;
    this.isVerifying = false;
    this.progress = 100;
    this.statusMessage = 'Transcripción existente cargada';

    // Mostrar notificación
    this.snackBar.open(
      '✅ Se cargó una transcripción existente para este lead',
      'Cerrar',
      {
        duration: 4000,
        panelClass: ['success-snackbar'],
      }
    );
  }

  /**
   * Carga la transcripción existente desde la respuesta de verificación
   */
  private cargarTranscripcionExistenteDesdeVerificacion(verificationResponse: any): void {
    // Obtener los datos completos del lead para tener el texto de transcripción real
    this.obtenerDatosCompletosLead().then((datosCompletosLead) => {
      // Usar el mismo campo que usa el flujo de PC: textoTranscription
      const textoTranscripcion = datosCompletosLead?.textoTranscription || 'Transcripción existente (texto no disponible)';

      // Debug: Ver si se obtuvo el texto correctamente
      console.log('Texto de transcripción obtenido:', textoTranscripcion);

      // Crear un resultado de transcripción con los datos reales
      this.transcriptionResult = {
        success: true,
        call_id: `EXISTING_${this.data.numeroMovil}_${new Date().getTime()}`,
        transcription: textoTranscripcion,
        mood: 'neutral',
        mood_confidence: 0.85,
        sentiment_score: 0.0,
        call_duration: 0,
        processing_time: 0,
        audio_quality_score: 0.95,
        word_count: textoTranscripcion !== 'Transcripción existente (texto no disponible)' ? textoTranscripcion.split(' ').length : 0,
        language_detected: 'es',
        model_used: 'existing',
        device_used: 'database',
        file_size_mb: 0,
        created_at: datosCompletosLead?.fechaCreacion || new Date().toISOString(),
        call_datetime: datosCompletosLead?.fechaCreacion || new Date().toISOString()
      } as any;

      // Agregar propiedades adicionales
      (this.transcriptionResult as any).urlDriveTranscripcion = datosCompletosLead?.urlDriveTranscripcion || verificationResponse.urlDriveTranscripcion;
      (this.transcriptionResult as any).notaAgenteComparadorIA = datosCompletosLead?.notaAgenteComparadorIA || verificationResponse.notaAgenteComparadorIA;
      (this.transcriptionResult as any).datosLeadCompletos = datosCompletosLead;

      // Cambiar al estado de completado
      this.currentStep = 'completed';
      this.isProcessing = false;
      this.isVerifying = false;
      this.progress = 100;
      this.statusMessage = 'Transcripción existente cargada';

      // Mostrar notificación de éxito (igual que cargarTranscripcionExistente)
      this.snackBar.open(
        '✅ Se cargó una transcripción existente para este lead',
        'Cerrar',
        {
          duration: 4000,
          panelClass: ['success-snackbar'],
        }
      );
    }).catch(() => {
      // Si falla la obtención de datos completos, usar los datos básicos de verificación
      this.transcriptionResult = {
        success: true,
        call_id: `EXISTING_${this.data.numeroMovil}_${new Date().getTime()}`,
        transcription: verificationResponse.textoTranscripcion || 'Transcripción existente (texto no disponible)',
        mood: 'neutral',
        mood_confidence: 0.85,
        sentiment_score: 0.0,
        call_duration: 0,
        processing_time: 0,
        audio_quality_score: 0.95,
        word_count: verificationResponse.textoTranscripcion ? verificationResponse.textoTranscripcion.split(' ').length : 0,
        language_detected: 'es',
        model_used: 'existing',
        device_used: 'database',
        file_size_mb: 0,
        created_at: new Date().toISOString(),
        call_datetime: new Date().toISOString()
      } as any;

      // Agregar propiedades adicionales
      (this.transcriptionResult as any).urlDriveTranscripcion = verificationResponse.urlDriveTranscripcion;
      (this.transcriptionResult as any).notaAgenteComparadorIA = verificationResponse.notaAgenteComparadorIA;

      // Cambiar al estado de completado
      this.currentStep = 'completed';
      this.isProcessing = false;
      this.isVerifying = false;
      this.progress = 100;
      this.statusMessage = 'Transcripción existente cargada';

      // Mostrar notificación de éxito (igual que cargarTranscripcionExistente)
      this.snackBar.open(
        '✅ Se cargó una transcripción existente para este lead',
        'Cerrar',
        {
          duration: 4000,
          panelClass: ['success-snackbar'],
        }
      );
    });
  }

  /**
   * Verifica si el cliente ya tiene una transcripción registrada (método original)
   */
  verificarTranscripcionExistente(): void {
    if (!this.data.numeroMovil) {
      return;
    }

    this.isVerifying = true;
    this.statusMessage = 'Verificando transcripciones existentes...';

    this.transcriptionSaveService
      .verificarTranscripcion(this.data.numeroMovil)
      .subscribe({
        next: (response) => {
          this.verificationResult = response;
          this.isVerifying = false;

          if (response.tieneTranscripcion) {
            this.showExistingTranscriptionWarning = true;
            this.statusMessage = response.mensaje;

            // Cargar automáticamente la transcripción existente
            this.cargarTranscripcionExistenteDesdeVerificacion(response);
          } else {
            this.statusMessage = 'Listo para transcribir';
          }
        },
        error: () => {
          this.isVerifying = false;
          this.statusMessage = 'Error al verificar transcripción existente';

          // No bloquear el proceso si hay error en la verificación
          this.showError(
            'No se pudo verificar si existe una transcripción previa'
          );
        },
      });
  }

  /**
   * Inicia el proceso de transcripción
   */
  async startTranscription(): Promise<void> {
    try {
      // Validar que hay un archivo (de Google Drive o subido)
      if (!this.data.file && !this.uploadedFile) {
        this.snackBar.open(
          'Por favor selecciona un archivo de audio',
          'Cerrar',
          {
            duration: 3000,
            panelClass: ['error-snackbar'],
          }
        );
        return;
      }

      // Si ya existe una transcripción, confirmar antes de proceder
      if (this.verificationResult?.tieneTranscripcion) {
        const confirmed = await this.confirmOverwriteTranscription();
        if (!confirmed) {
          return; // Usuario canceló
        }
      }

      this.isProcessing = true;
      this.currentStep = 'transcribing';
      this.progress = 10;

      let audioFile: File;

      if (this.uploadedFile) {
        // Usar archivo subido directamente
        this.statusMessage = 'Preparando archivo subido...';
        audioFile = this.uploadedFile;
        this.progress = 25;
      } else {
        // Descargar archivo desde Google Drive
        this.statusMessage = 'Descargando archivo desde Google Drive...';
        audioFile = await this.createFileFromGoogleDrive();
        this.progress = 25;
      }

      this.progress = 25;
      this.statusMessage = 'Enviando archivo al servidor de transcripción...';

      // Preparar la solicitud de transcripción
      const request: TranscriptionRequest = {
        audio_file: audioFile,
        whisper_model: this.transcriptionConfig.whisper_model,
        device: this.transcriptionConfig.device,
        target_language: this.transcriptionConfig.target_language,
        call_id: this.generateCallId(),
        call_type: this.transcriptionConfig.call_type,
        caller_phone: this.data.numeroMovil,
        agent_id: this.transcriptionConfig.agent_id,
        call_datetime: this.transcriptionConfig.call_datetime,
      };

      // Validar la solicitud
      const validation =
        this.transcriptionService.validateTranscriptionRequest(request);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }

      this.progress = 50;
      this.statusMessage =
        'Transcribiendo audio con IA... Esto puede tomar varios minutos.';

      // Realizar la transcripción - ESPERAR RESPUESTA REAL DEL SERVIDOR
      this.transcriptionService.transcribeAudio(request).subscribe({
        next: (response) => {
          // Guardar la transcripción en el cliente residencial
          this.transcriptionResult = response;
          const callId = request.call_id || this.generateCallId();
          this.saveTranscriptionToClient(response, callId);
        },
        error: (error) => {
          console.error('=== ERROR EN TRANSCRIPCIÓN ===');
          console.error('Error completo:', error);
          console.error('Status:', error.status);
          console.error('Status Text:', error.statusText);
          console.error('URL:', error.url);

          // Intentar mostrar el mensaje de error específico del servidor
          let errorMessage =
            'Error al procesar la transcripción. Por favor, intente nuevamente.';
          if (error.error && error.error.error) {
            errorMessage = error.error.error;
            if (error.error.recommendation) {
              errorMessage += ` - ${error.error.recommendation}`;
            }

            // Si es un error de Numpy, dar sugerencias específicas
            if (error.error.error.includes('Numpy is not available')) {
              errorMessage += '\n\nSugerencias:\n';
              errorMessage +=
                '• El servidor tiene problemas con las dependencias de Python\n';
              errorMessage +=
                '• Contacte al administrador del sistema para verificar la instalación de Numpy\n';
              errorMessage +=
                '• Intente nuevamente más tarde cuando el servidor esté funcionando correctamente';
            }
          } else if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          console.error('Mensaje de error final:', errorMessage);

          this.currentStep = 'error';
          this.isProcessing = false;
          this.statusMessage = errorMessage;
          this.showError('Error al procesar la transcripción');
        },
      });
    } catch (error) {
      this.currentStep = 'error';
      this.isProcessing = false;
      this.statusMessage = 'Error: ' + (error as Error).message;
      this.showError('Error al iniciar la transcripción');
    }
  }

  /**
   * Descarga el archivo real desde Google Drive usando el servicio
   */
  private async createFileFromGoogleDrive(): Promise<File> {
    if (!this.data.file) {
      throw new Error('No hay archivo de Google Drive disponible');
    }

    return new Promise((resolve, reject) => {
      this.googleDriveService.downloadFile(this.data.file!.id).subscribe({
        next: (blob) => {
          // Crear el archivo con el blob descargado
          const file = new File([blob], this.data.file!.name, {
            type: this.data.file!.mimeType || 'audio/mpeg',
          });

          resolve(file);
        },
        error: (error) => {
          reject(
            new Error(
              `No se pudo descargar el archivo: ${error.message || error}`
            )
          );
        },
      });
    });
  }

  /**
   * Genera un ID único para la llamada
   */
  private generateCallId(): string {
    return this.transcriptionService.generateCallId(this.data.numeroMovil);
  }

  /**
   * Obtiene el nombre completo del cliente
   */
  getClientName(): string {
    if (this.data.cliente) {
      return `${this.data.cliente.nombres} ${this.data.cliente.apellidos}`;
    }
    return 'Cliente no especificado';
  }

  /**
   * Formatea la fecha de creación del lead
   */
  formatFechaCreacion(fecha: string): string {
    if (!fecha) return 'N/A';
    try {
      const date = new Date(fecha);
      if (isNaN(date.getTime())) return 'Fecha inválida';

      return date.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return 'Fecha inválida';
    }
  }

  /**
   * Obtiene el color del estado de ánimo
   */
  getSentimentColor(sentiment: string): string {
    return this.transcriptionService.getSentimentColor(sentiment);
  }

  /**
   * Obtiene el icono del estado de ánimo
   */
  getSentimentIcon(sentiment: string): string {
    return this.transcriptionService.getSentimentIcon(sentiment);
  }

  /**
   * Traduce el estado de ánimo
   */
  translateSentiment(sentiment: string): string {
    return this.transcriptionService.translateSentiment(sentiment);
  }

  /**
   * Formatea la duración del audio
   */
  formatDuration(seconds: number): string {
    return this.transcriptionService.formatDuration(seconds);
  }

  /**
   * Formatea el tiempo de procesamiento
   */
  formatProcessingTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
    }
  }

  /**
   * Obtiene el color para la calidad del audio
   */
  getAudioQualityColor(score: number): string {
    if (score >= 0.8) return '#4caf50'; // Verde
    if (score >= 0.6) return '#ff9800'; // Naranja
    if (score >= 0.4) return '#f44336'; // Rojo
    return '#9e9e9e'; // Gris
  }

  /**
   * Obtiene la descripción de la calidad del audio
   */
  getAudioQualityDescription(score: number): string {
    if (score >= 0.8) return 'Excelente';
    if (score >= 0.6) return 'Buena';
    if (score >= 0.4) return 'Regular';
    if (score >= 0.2) return 'Mala';
    return 'Muy mala';
  }

  /**
   * Alterna la visualización de información avanzada
   */
  toggleAdvancedInfo(): void {
    this.showAdvancedInfo = !this.showAdvancedInfo;
  }

  /**
   * Copia la transcripción al portapapeles
   */
  copyTranscription(): void {
    if (this.transcriptionResult?.transcription) {
      navigator.clipboard
        .writeText(this.transcriptionResult.transcription)
        .then(() => {
          this.showSuccess('Transcripción copiada al portapapeles');
        })
        .catch(() => {
          this.showError('Error al copiar la transcripción');
        });
    }
  }

  /**
   * Confirma si el usuario quiere sobrescribir la transcripción existente
   */
  private confirmOverwriteTranscription(): Promise<boolean> {
    return new Promise((resolve) => {
      const snackBarRef = this.snackBar.open(
        '⚠️ Este cliente ya tiene una transcripción. ¿Desea sobrescribirla?',
        'SÍ, SOBRESCRIBIR',
        {
          duration: 15000, // 15 segundos para decidir
          panelClass: ['warning-snackbar'],
          horizontalPosition: 'center',
          verticalPosition: 'top',
        }
      );

      // Si hace clic en el botón, confirma
      snackBarRef.onAction().subscribe(() => {
        resolve(true);
      });

      // Si se cierra sin acción, cancela
      snackBarRef.afterDismissed().subscribe((dismissedByAction) => {
        if (!dismissedByAction.dismissedByAction) {
          resolve(false);
        }
      });
    });
  }

  /**
   * Obtiene información de la transcripción existente para mostrar al usuario
   */
  getExistingTranscriptionInfo(): string {
    if (!this.verificationResult) {
      return '';
    }

    let info = `Cliente: ${
      this.verificationResult.nombreCliente || 'No especificado'
    }`;

    if (this.verificationResult.tieneTextoTranscripcion) {
      info += '\n✓ Tiene texto de transcripción';
    }

    if (this.verificationResult.tieneUrlDriveTranscripcion) {
      info += '\n✓ Tiene archivo en Google Drive';
    }

    return info;
  }

  /**
   * Abre la transcripción existente en Google Drive (si existe)
   */
  openExistingTranscription(): void {
    if (this.verificationResult?.urlDriveTranscripcion) {
      window.open(this.verificationResult.urlDriveTranscripcion, '_blank');
    }
  }

  /**
   * Reinicia el proceso para una nueva transcripción
   */
  resetTranscription(): void {
    this.currentStep = 'config';
    this.progress = 0;
    this.statusMessage = '';
    this.transcriptionResult = null;
    this.isProcessing = false;
    this.showExistingTranscriptionWarning = false;

    // Volver a verificar transcripción
    if (this.data.numeroMovil) {
      this.verificarTranscripcionExistente();
    }
  }

  /**
   * Guarda la transcripción en el cliente residencial
   */
  private saveTranscriptionToClient(response: any, callId: string): void {
    if (!this.data.numeroMovil) {
      this.completeTranscription();
      return;
    }

    // Extraer el texto de transcripción de la respuesta
    let transcriptionText = '';
    if (response.transcription) {
      transcriptionText = response.transcription;
    } else {
      this.completeTranscription();
      return;
    }

    this.progress = 75;
    this.statusMessage = 'Guardando transcripción en el cliente...';

    const saveRequest: TranscriptionSaveRequest = {
      numeroMovil: this.data.numeroMovil,
      transcriptionText: transcriptionText,
      callId: callId,
      fileName: this.generateTranscriptionFileName(callId),
      forceOverwrite: this.verificationResult?.tieneTranscripcion || false,
    };

    this.transcriptionSaveService.saveTranscription(saveRequest).subscribe({
      next: (saveResponse) => {
        if (saveResponse.success) {
          this.showSuccess('Transcripción guardada en el cliente exitosamente');
          this.completeTranscription();
        } else if (saveResponse.error === 'TRANSCRIPTION_ALREADY_EXISTS') {
          // El cliente ya tiene una transcripción
          this.handleExistingTranscription(saveResponse, saveRequest);
        } else {
          this.showError(
            'Error al guardar la transcripción: ' +
              (saveResponse.error || saveResponse.message)
          );
          this.completeTranscription(); // Completar de todas formas
        }
      },
      error: () => {
        this.showError('Error al guardar la transcripción en el cliente');
        this.completeTranscription(); // Completar de todas formas
      },
    });
  }

  /**
   * Completa el proceso de transcripción
   */
  private completeTranscription(): void {
    this.currentStep = 'completed';
    this.progress = 100;
    this.statusMessage = 'Transcripción completada exitosamente';
    this.isProcessing = false;
  }

  /**
   * Maneja el caso cuando el cliente ya tiene una transcripción
   */
  private handleExistingTranscription(
    _saveResponse: any,
    originalRequest: any
  ): void {
    this.progress = 90;
    this.statusMessage = 'Cliente ya tiene transcripción registrada';

    // Mostrar mensaje informativo con opciones usando MatSnackBar
    const snackBarRef = this.snackBar.open(
      'Este cliente ya tiene una transcripción registrada. ¿Desea sobrescribirla?',
      'SOBRESCRIBIR',
      {
        duration: 10000, // 10 segundos para que el usuario pueda decidir
        panelClass: ['warning-snackbar'],
        horizontalPosition: 'center',
        verticalPosition: 'top',
      }
    );

    // Manejar la acción del usuario
    snackBarRef.onAction().subscribe(() => {
      // Usuario quiere sobrescribir
      this.overwriteExistingTranscription(originalRequest);
    });

    // Si el snackbar se cierra sin acción, completar sin sobrescribir
    snackBarRef.afterDismissed().subscribe((dismissedByAction) => {
      if (!dismissedByAction.dismissedByAction) {
        // Usuario no quiere sobrescribir (cerró el snackbar sin hacer clic en SOBRESCRIBIR)
        this.showSuccess(
          'Transcripción completada. El cliente mantiene su transcripción anterior.'
        );
        this.completeTranscription();
      }
    });
  }

  /**
   * Sobrescribe la transcripción existente
   */
  private overwriteExistingTranscription(originalRequest: any): void {
    this.progress = 75;
    this.statusMessage = 'Sobrescribiendo transcripción existente...';

    const overwriteRequest = {
      ...originalRequest,
      forceOverwrite: true,
    };

    this.transcriptionSaveService
      .saveTranscription(overwriteRequest)
      .subscribe({
        next: (overwriteResponse) => {
          if (overwriteResponse.success) {
            this.showSuccess('Transcripción sobrescrita exitosamente');
            this.completeTranscription();
          } else {
            this.showError(
              'Error al sobrescribir la transcripción: ' +
                (overwriteResponse.error || overwriteResponse.message)
            );
            this.completeTranscription();
          }
        },
        error: () => {
          this.showError('Error al sobrescribir la transcripción');
          this.completeTranscription();
        },
      });
  }

  /**
   * Genera un nombre de archivo para la transcripción
   */
  private generateTranscriptionFileName(callId: string): string {
    const timestamp = new Date()
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, '');
    let baseName = '';

    if (this.uploadedFile) {
      // Usar el nombre del archivo subido (sin extensión)
      baseName = this.uploadedFile.name.replace(/\.[^/.]+$/, '');
    } else if (this.data.file) {
      // Usar el nombre del archivo de Google Drive (sin extensión)
      baseName = this.data.file.name.replace(/\.[^/.]+$/, '');
    } else {
      baseName = 'audio';
    }

    return `transcripcion_${this.data.numeroMovil}_${baseName}_${callId}_${timestamp}.txt`;
  }

  /**
   * Cierra el diálogo
   */
  close(): void {
    this.dialogRef.close(this.transcriptionResult);
  }

  /**
   * Muestra mensaje de éxito
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 3000,
      panelClass: ['success-snackbar'],
    });
  }

  /**
   * Muestra mensaje de error
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 5000,
      panelClass: ['error-snackbar'],
    });
  }

  /**
   * Maneja la subida de archivos por drag & drop
   */
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  /**
   * Maneja la selección de archivos desde el input
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFileSelection(input.files[0]);
    }
  }

  /**
   * Procesa el archivo seleccionado
   */
  handleFileSelection(file: File): void {
    // Validar que sea un archivo de audio
    if (!this.isValidAudioFile(file)) {
      this.snackBar.open(
        'Por favor selecciona un archivo de audio válido (.mp3, .wav, .m4a, .ogg, .flac)',
        'Cerrar',
        {
          duration: 5000,
          panelClass: ['error-snackbar'],
        }
      );
      return;
    }

    // Validar tamaño del archivo (máximo 100MB)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      this.snackBar.open(
        'El archivo es demasiado grande. Máximo permitido: 100MB',
        'Cerrar',
        {
          duration: 5000,
          panelClass: ['error-snackbar'],
        }
      );
      return;
    }

    this.uploadedFile = file;
  }

  /**
   * Valida si el archivo es de audio
   */
  isValidAudioFile(file: File): boolean {
    const validTypes = [
      'audio/mpeg',
      'audio/wav',
      'audio/mp4',
      'audio/ogg',
      'audio/flac',
      'audio/x-m4a',
    ];

    const validExtensions = ['.mp3', '.wav', '.m4a', '.ogg', '.flac'];
    const fileName = file.name.toLowerCase();

    return (
      validTypes.includes(file.type) ||
      validExtensions.some((ext) => fileName.endsWith(ext))
    );
  }

  /**
   * Elimina el archivo seleccionado
   */
  removeUploadedFile(): void {
    this.uploadedFile = null;
  }

  /**
   * Formatea el tamaño del archivo
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Abre el selector de archivos del sistema
   */
  openFileSelector(event: Event): void {
    event.stopPropagation(); // Evitar que se active el click del área de drag & drop
    // Usar el ViewChild para acceder al input file
    const fileInputElement = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    if (fileInputElement) {
      fileInputElement.click();
    }
  }

  /**
   * Elimina el archivo seleccionado de Google Drive
   */
  removeGoogleDriveFile(): void {
    this.data.file = undefined;
    this.snackBar.open('Archivo de Google Drive eliminado', 'Cerrar', {
      duration: 3000,
      panelClass: ['info-snackbar'],
    });
  }

  /**
   * Obtiene la extensión de un archivo
   */
  getFileExtension(fileName: string): string {
    if (!fileName) return '';
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot) : '';
  }

  /**
   * Abre el explorador de Google Drive para seleccionar archivos de audio
   */
  openGoogleDriveExplorer(event?: Event): void {
    // Prevenir la propagación del evento para evitar que se active el selector de archivos nativo
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const dialogRef = this.dialog.open(GoogleDriveExplorerComponent, {
      width: '90vw',
      height: '80vh',
      maxWidth: '1200px',
      maxHeight: '800px',
      data: {
        mode: 'select', // Modo selección para elegir archivos existentes
        allowedTypes: ['audio/*'], // Solo archivos de audio
        title: 'Seleccionar Audio desde Google Drive',
        multiSelect: false, // Solo un archivo
        cliente: this.data.cliente,
        numeroMovil: this.data.numeroMovil,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (
        result &&
        result.selected &&
        result.files &&
        result.files.length > 0
      ) {
        const selectedFile = result.files[0];

        // Asignar el archivo seleccionado a data.file para que sea usado en la transcripción
        this.data.file = selectedFile;

        // Limpiar archivo subido localmente si existe
        this.uploadedFile = null;

        // Mostrar notificación de éxito
        this.snackBar.open(
          `✅ Archivo seleccionado: ${selectedFile.name}`,
          'Cerrar',
          {
            duration: 4000,
            panelClass: ['success-snackbar'],
          }
        );
      }
    });
  }

  /**
   * Compara la transcripción con los datos del lead
   */
  compararLead(): void {
    // Validar que hay transcripción disponible
    if (!this.transcriptionResult?.transcription) {
      this.snackBar.open(
        'No hay transcripción disponible para comparar',
        'Cerrar',
        {
          duration: 3000,
          panelClass: ['error-snackbar'],
        }
      );
      return;
    }

    // Validar que hay datos del cliente
    if (!this.data.cliente || !this.data.numeroMovil) {
      this.snackBar.open(
        'No hay datos del cliente disponibles para comparar',
        'Cerrar',
        {
          duration: 3000,
          panelClass: ['error-snackbar'],
        }
      );
      return;
    }

    // Mostrar mensaje de procesamiento inicial
    this.snackBar.open('🔄 Obteniendo datos completos del lead...', '', {
      duration: 3000,
      panelClass: ['info-snackbar'],
    });

    // Primero obtener los datos completos del lead desde la API de detalle
    this.obtenerDatosCompletosLead()
      .then((datosCompletosLead) => {
        // Mostrar mensaje de comparación
        this.snackBar.open(
          '🔄 Comparando transcripción con datos del lead...',
          '',
          {
            duration: 2000,
            panelClass: ['info-snackbar'],
          }
        );

        // Preparar los datos para enviar a la API de comparación
        const requestData = {
          texto_audio: this.transcriptionResult?.transcription || '',
          datos_lead: datosCompletosLead, // Usar los datos completos obtenidos de la API
        };

        // Enviar datos a la API de comparación
        this.http
          .post(`${environment.urlVentas}comparar/`, requestData)
          .subscribe({
            next: (response: any) => {
              // Extraer el porcentaje de coincidencia de la respuesta
              const porcentajeCoincidencia =
                response.porcentaje_coincidencia || 0;

              // Buscar el ID del cliente en nuestra base de datos local usando el número móvil
              // Usar el número móvil disponible en this.data.numeroMovil como fallback
              const numeroMovilParaBuscar =
                datosCompletosLead.numeroMovil ||
                datosCompletosLead.movilContacto ||
                this.data.numeroMovil;

              this.buscarClienteIdPorMovil(numeroMovilParaBuscar)
                .then((clienteId) => {
                  if (!clienteId) {
                    // Mostrar snackbar con el porcentaje aunque no se haya guardado
                    this.snackBar.open(
                      `⚠️ Comparación completada: ${porcentajeCoincidencia}% (no se pudo guardar - ID no encontrado)`,
                      'Cerrar',
                      {
                        duration: 5000,
                        panelClass: ['warning-snackbar'],
                      }
                    );
                    return;
                  }

                  // Guardar el porcentaje en la base de datos
                  this.guardarPorcentajeComparacion(
                    clienteId,
                    porcentajeCoincidencia
                  )
                    .then(() => {
                      // Mostrar resultado exitoso
                      import('sweetalert2').then((Swal) => {
                        // Determinar el color y mensaje según el porcentaje
                        let colorPorcentaje = '#059669'; // Verde por defecto
                        let mensajeCalidad = 'Excelente coincidencia';
                        let iconoCalidad = '🎯';

                        if (porcentajeCoincidencia >= 80) {
                          colorPorcentaje = '#059669'; // Verde
                          mensajeCalidad = 'Excelente coincidencia';
                          iconoCalidad = '🎯';
                        } else if (porcentajeCoincidencia >= 60) {
                          colorPorcentaje = '#0891b2'; // Azul
                          mensajeCalidad = 'Buena coincidencia';
                          iconoCalidad = '✅';
                        } else if (porcentajeCoincidencia >= 40) {
                          colorPorcentaje = '#ea580c'; // Naranja
                          mensajeCalidad = 'Coincidencia moderada';
                          iconoCalidad = '⚠️';
                        } else {
                          colorPorcentaje = '#dc2626'; // Rojo
                          mensajeCalidad = 'Baja coincidencia';
                          iconoCalidad = '❌';
                        }

                        Swal.default.fire({
                          title: '✅ Comparación Completada',
                          html: `
              <div style="text-align: center; padding: 30px;">
                <div style="margin-bottom: 30px;">
                  <div style="font-size: 72px; margin-bottom: 15px;">${iconoCalidad}</div>
                  <h2 style="color: ${colorPorcentaje}; margin: 0 0 10px 0; font-size: 48px; font-weight: bold;">
                    ${porcentajeCoincidencia}%
                  </h2>
                  <h3 style="color: #374151; margin: 0 0 20px 0; font-size: 24px; font-weight: 500;">
                    ${mensajeCalidad}
                  </h3>
                </div>

                <div style="background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); border: 2px solid ${colorPorcentaje}; border-radius: 16px; padding: 25px; margin: 20px 0;">
                  <h4 style="color: ${colorPorcentaje}; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
                    📊 Porcentaje de Coincidencia
                  </h4>
                  <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                      <div style="width: 200px; height: 20px; background: #e5e7eb; border-radius: 10px; overflow: hidden; position: relative;">
                        <div style="width: ${porcentajeCoincidencia}%; height: 100%; background: linear-gradient(90deg, ${colorPorcentaje} 0%, ${colorPorcentaje}dd 100%); border-radius: 10px; transition: width 0.3s ease;"></div>
                      </div>
                      <span style="font-size: 20px; font-weight: bold; color: ${colorPorcentaje};">${porcentajeCoincidencia}%</span>
                    </div>
                  </div>
                </div>

                <p style="color: #6b7280; font-size: 16px; margin: 20px 0 0 0; line-height: 1.5;">
                  La transcripción del audio ha sido comparada exitosamente con los datos del lead.
                </p>
              </div>
            `,
                          icon: 'success',
                          iconColor: colorPorcentaje,
                          confirmButtonText: 'Entendido',
                          confirmButtonColor: colorPorcentaje,
                          width: '500px',
                          customClass: {
                            popup: 'swal-custom-popup',
                          },
                        });
                      });

                      // Mostrar snackbar con el porcentaje
                      this.snackBar.open(
                        `✅ Comparación completada: ${porcentajeCoincidencia}% de coincidencia`,
                        'Cerrar',
                        {
                          duration: 4000,
                          panelClass: ['success-snackbar'],
                        }
                      );
                    })
                    .catch(() => {
                      // Mostrar snackbar con el porcentaje aunque no se haya guardado
                      this.snackBar.open(
                        `⚠️ Comparación completada: ${porcentajeCoincidencia}% (no guardado en BD)`,
                        'Cerrar',
                        {
                          duration: 5000,
                          panelClass: ['warning-snackbar'],
                        }
                      );
                    });
                })
                .catch(() => {
                  // Mostrar snackbar con el porcentaje aunque no se haya guardado
                  this.snackBar.open(
                    `⚠️ Comparación completada: ${porcentajeCoincidencia}% (error al buscar cliente)`,
                    'Cerrar',
                    {
                      duration: 5000,
                      panelClass: ['warning-snackbar'],
                    }
                  );
                });
            },
            error: (error) => {
              let errorMessage = 'Error al comparar con la API';
              if (error.error && error.error.detail) {
                errorMessage = error.error.detail;
              } else if (error.message) {
                errorMessage = error.message;
              }

              // Mostrar error detallado
              import('sweetalert2').then((Swal) => {
                Swal.default.fire({
                  title: '❌ Error en la Comparación',
                  html: `
              <div style="text-align: left; padding: 20px;">
                <h4 style="color: #dc2626; margin-bottom: 15px;">🚨 Error al procesar</h4>
                <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                  <p style="color: #991b1b; margin: 0; font-weight: 500;">${errorMessage}</p>
                </div>
                <p style="color: #6b7280; font-size: 14px; margin: 0;">
                  Verifica que la API esté funcionando correctamente en:
                  <code style="background: #f3f4f6; padding: 2px 4px; border-radius: 4px;">http://127.0.0.1:8000/api/comparar/</code>
                </p>
              </div>
            `,
                  icon: 'error',
                  iconColor: '#dc2626',
                  confirmButtonText: 'Entendido',
                  confirmButtonColor: '#dc2626',
                  width: '600px',
                });
              });

              this.snackBar.open(`❌ Error: ${errorMessage}`, 'Cerrar', {
                duration: 5000,
                panelClass: ['error-snackbar'],
              });
            },
          });
      })
      .catch(() => {
        this.snackBar.open(
          '❌ Error al obtener los datos del lead para comparar',
          'Cerrar',
          {
            duration: 5000,
            panelClass: ['error-snackbar'],
          }
        );
      });
  }

  /**
   * Obtiene los datos completos del lead desde la API de detalle
   */
  private obtenerDatosCompletosLead(): Promise<any> {
    return new Promise((resolve) => {
      // Usar el DNI del cliente para la consulta
      const dni = this.data.dniCliente; // DNI del cliente
      const movil = this.data.numeroMovil; // Número móvil del cliente
      const fechaCreacion = this.data.fechaCreacion; // Fecha de creación del lead

      // Validar que tenemos los datos necesarios
      if (!dni || !movil) {
        resolve(null);
        return;
      }

      // Construir la URL de la API usando environment
      const apiUrl = `${environment.url}api/cliente-promocion/detalle?dni=${dni}&movil=${movil}&fechaCreacion=${fechaCreacion}`;

      this.http.get(apiUrl).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: () => {
          // En caso de error, usar datos básicos disponibles
          const datosBasicos = {
            nombres: this.data.cliente?.nombres || '',
            apellidos: this.data.cliente?.apellidos || '',
            numeroMovil: this.data.numeroMovil,
            dni: dni,
            fechaCreacion: fechaCreacion,
            error_obtencion_datos: true,
            mensaje_error:
              'No se pudieron obtener los datos completos del lead',
            transcription_metadata: {
              call_id: this.transcriptionResult?.call_id,
              mood: this.transcriptionResult?.mood,
              mood_confidence: this.transcriptionResult?.mood_confidence,
              sentiment_score: this.transcriptionResult?.sentiment_score,
              call_duration: this.transcriptionResult?.call_duration,
              processing_time: this.transcriptionResult?.processing_time,
              audio_quality_score:
                this.transcriptionResult?.audio_quality_score,
              word_count: this.transcriptionResult?.word_count,
              language_detected: this.transcriptionResult?.language_detected,
              model_used: this.transcriptionResult?.model_used,
              device_used: this.transcriptionResult?.device_used,
              file_size_mb: this.transcriptionResult?.file_size_mb,
              created_at: this.transcriptionResult?.created_at,
              call_datetime: this.transcriptionResult?.call_datetime,
            },
          };

          resolve(datosBasicos);
        },
      });
    });
  }

  /**
   * Busca el ID del cliente en nuestra base de datos local usando el número móvil
   */
  private buscarClienteIdPorMovil(numeroMovil: string): Promise<number | null> {
    return new Promise((resolve) => {
      if (!numeroMovil) {
        resolve(null);
        return;
      }

      // Buscar directamente en la API
      const searchUrl = `${environment.url}api/clientes/buscar-por-movil/${numeroMovil}`;

      this.http
        .get<GenericResponse<ClienteResidencial[]>>(searchUrl)
        .subscribe({
          next: (response) => {
            if (
              response.rpta === 1 &&
              response.data &&
              response.data.length > 0
            ) {
              // Tomar el primer cliente encontrado
              const cliente = response.data[0];
              resolve(cliente.id);
            } else {
              resolve(null);
            }
          },
          error: () => {
            resolve(null);
          },
        });
    });
  }

  /**
   * Guarda el porcentaje de comparación en la base de datos
   */
  private guardarPorcentajeComparacion(
    clienteId: number,
    porcentaje: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const requestBody = {
        notaAgenteComparadorIA: porcentaje,
      };

      this.http
        .put<GenericResponse<ClienteResidencial>>(
          `${environment.url}api/clientes/${clienteId}/nota-agente-comparador-ia`,
          requestBody
        )
        .subscribe({
          next: (response) => {
            if (response.rpta === 1) {
              resolve();
            } else {
              reject(new Error(response.msg));
            }
          },
          error: (error) => {
            reject(error);
          },
        });
    });
  }
}
