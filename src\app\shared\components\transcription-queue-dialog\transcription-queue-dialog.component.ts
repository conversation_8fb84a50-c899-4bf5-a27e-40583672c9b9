import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormControl } from '@angular/forms';
import { Subject, interval } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { 
  TranscriptionQueueService, 
  TranscriptionQueueMessage, 
  QueueStatistics, 
  ProcessingResult 
} from '../../../services/transcription-queue.service';

export interface TranscriptionQueueDialogData {
  title?: string;
  numeroAgente?: string;
  batchSize?: number;
}

@Component({
  selector: 'app-transcription-queue-dialog',
  templateUrl: './transcription-queue-dialog.component.html',
  styleUrls: ['./transcription-queue-dialog.component.scss']
})
export class TranscriptionQueueDialogComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Controles de formulario
  numeroAgenteControl = new FormControl('');
  batchSizeControl = new FormControl(3);

  // Estado del componente
  loading = false;
  processing = false;
  pendingLeads: TranscriptionQueueMessage[] = [];
  selectedLeads: TranscriptionQueueMessage[] = [];
  statistics: QueueStatistics | null = null;
  lastProcessingResult: ProcessingResult | null = null;

  // Configuración de tabla
  displayedColumns: string[] = ['select', 'leadId', 'numeroMovil', 'numeroAgente', 'nombreAsesor', 'fechaCreacion', 'actions'];

  // Estado de procesamiento
  processingStatus = {
    currentStep: '',
    progress: 0,
    message: ''
  };

  constructor(
    public dialogRef: MatDialogRef<TranscriptionQueueDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TranscriptionQueueDialogData,
    private transcriptionQueueService: TranscriptionQueueService,
    private snackBar: MatSnackBar
  ) {
    // Inicializar con datos del diálogo
    if (data.numeroAgente) {
      this.numeroAgenteControl.setValue(data.numeroAgente);
    }
    if (data.batchSize) {
      this.batchSizeControl.setValue(data.batchSize);
    }
  }

  ngOnInit(): void {
    this.setupFormControls();
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupFormControls(): void {
    // Búsqueda en tiempo real por agente
    this.numeroAgenteControl.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.loadPendingLeads();
      });
  }

  private loadInitialData(): void {
    this.loadStatistics();
    this.loadPendingLeads();
  }

  /**
   * Carga estadísticas de las colas
   */
  loadStatistics(): void {
    this.transcriptionQueueService.getQueueStatistics()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.statistics = response.data;
          } else {
            this.showError('Error al cargar estadísticas: ' + response.msg);
          }
        },
        error: (error) => {
          console.error('Error al cargar estadísticas:', error);
          this.showError('Error al cargar estadísticas');
        }
      });
  }

  /**
   * Carga leads pendientes de transcripción
   */
  loadPendingLeads(): void {
    this.loading = true;
    const numeroAgente = this.numeroAgenteControl.value || undefined;

    this.transcriptionQueueService.getPendingLeads(50, numeroAgente)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.pendingLeads = response.data;
            this.selectedLeads = []; // Limpiar selección
          } else {
            this.showError('Error al cargar leads: ' + response.msg);
          }
        },
        error: (error) => {
          this.loading = false;
          console.error('Error al cargar leads:', error);
          this.showError('Error al cargar leads pendientes');
        }
      });
  }

  /**
   * Procesa los leads seleccionados o todos si no hay selección
   */
  processSelectedLeads(): void {
    if (this.processing) return;

    const batchSize = this.batchSizeControl.value || 3;
    const numeroAgente = this.numeroAgenteControl.value || undefined;

    this.processing = true;
    this.processingStatus = {
      currentStep: 'Iniciando procesamiento...',
      progress: 10,
      message: 'Preparando leads para transcripción'
    };

    this.transcriptionQueueService.processPendingLeads(batchSize, numeroAgente)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.processing = false;
          if (response.rpta === 1) {
            this.lastProcessingResult = response.data;
            this.showSuccess(`Procesamiento completado: ${response.data.sentToQueue} leads enviados a la cola`);
            this.loadStatistics(); // Actualizar estadísticas
            this.loadPendingLeads(); // Actualizar lista
          } else {
            this.showError('Error en el procesamiento: ' + response.msg);
          }
        },
        error: (error) => {
          this.processing = false;
          console.error('Error en el procesamiento:', error);
          this.showError('Error al procesar leads');
        }
      });
  }

  /**
   * Busca archivo MP3 para un lead específico
   */
  searchMp3ForLead(lead: TranscriptionQueueMessage): void {
    this.transcriptionQueueService.findAudioByMovilAndAgent(
      lead.numeroMovil, 
      lead.numeroAgenteNormalizado
    ).subscribe({
      next: (response) => {
        if (response.rpta === 1) {
          this.showSuccess(`Archivo encontrado: ${response.data.audioUrl}`);
        } else {
          this.showWarning(`No se encontró archivo para el lead ${lead.leadId}`);
        }
      },
      error: (error) => {
        console.error('Error al buscar archivo:', error);
        this.showError('Error al buscar archivo de audio');
      }
    });
  }

  /**
   * Envía un lead específico a la cola
   */
  sendLeadToQueue(lead: TranscriptionQueueMessage): void {
    this.transcriptionQueueService.sendLeadToQueue(lead.leadId)
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            this.showSuccess(`Lead ${lead.leadId} enviado a la cola`);
            this.loadPendingLeads();
          } else {
            this.showError('Error al enviar lead: ' + response.msg);
          }
        },
        error: (error) => {
          console.error('Error al enviar lead:', error);
          this.showError('Error al enviar lead a la cola');
        }
      });
  }

  /**
   * Maneja la selección de leads
   */
  toggleLeadSelection(lead: TranscriptionQueueMessage): void {
    const index = this.selectedLeads.findIndex(l => l.leadId === lead.leadId);
    if (index > -1) {
      this.selectedLeads.splice(index, 1);
    } else {
      this.selectedLeads.push(lead);
    }
  }

  /**
   * Verifica si un lead está seleccionado
   */
  isLeadSelected(lead: TranscriptionQueueMessage): boolean {
    return this.selectedLeads.some(l => l.leadId === lead.leadId);
  }

  /**
   * Selecciona/deselecciona todos los leads
   */
  toggleAllSelection(): void {
    if (this.selectedLeads.length === this.pendingLeads.length) {
      this.selectedLeads = [];
    } else {
      this.selectedLeads = [...this.pendingLeads];
    }
  }

  /**
   * Verifica si todos los leads están seleccionados
   */
  isAllSelected(): boolean {
    return this.pendingLeads.length > 0 && this.selectedLeads.length === this.pendingLeads.length;
  }

  /**
   * Cierra el diálogo
   */
  close(): void {
    this.dialogRef.close();
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 5000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 7000,
      panelClass: ['error-snackbar']
    });
  }

  private showWarning(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 5000,
      panelClass: ['warning-snackbar']
    });
  }
}
