package com.midas.crm.controller;

import com.midas.crm.entity.DTO.leccion.LeccionCreateDTO;
import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import com.midas.crm.entity.DTO.leccion.LeccionUpdateDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.repository.LeccionRepository;
import com.midas.crm.service.LeccionService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.leccion}")
@Slf4j
public class LeccionController {

    private final LeccionService leccionService;
    private final LeccionRepository leccionRepository;
    private final RestTemplate restTemplate;

    public LeccionController(LeccionService leccionService,
                           LeccionRepository leccionRepository,
                           @Qualifier("standardRestTemplate") RestTemplate restTemplate) {
        this.leccionService = leccionService;
        this.leccionRepository = leccionRepository;
        this.restTemplate = restTemplate;
    }

    /**
     * Crea una nueva lección
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<LeccionDTO>> createLeccion(@Valid @RequestBody LeccionCreateDTO dto) {
        return Optional.ofNullable(dto)
                .map(leccionService::createLeccion)
                .map(leccion -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lección creada exitosamente", leccion)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Lista todas las lecciones
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<LeccionDTO>>> listLecciones() {
        return Optional.of(leccionService.listLecciones())
                .map(lecciones -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de lecciones", lecciones)
                ))
                .orElse(ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "No se encontraron lecciones", Collections.emptyList())
                ));
    }

    /**
     * Lista lecciones por sección
     * Implementado con programación funcional
     */
    @GetMapping("/seccion/{seccionId}")
    public ResponseEntity<GenericResponse<List<LeccionDTO>>> listLeccionesBySeccionId(@PathVariable Long seccionId) {
        return Optional.ofNullable(seccionId)
                .map(leccionService::listLeccionesBySeccionId)
                .map(lecciones -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de lecciones por sección", lecciones)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Lista lecciones por módulo
     * Implementado con programación funcional
     */
    @GetMapping("/modulo/{moduloId}")
    public ResponseEntity<GenericResponse<List<LeccionDTO>>> listLeccionesByModuloId(@PathVariable Long moduloId) {
        return Optional.ofNullable(moduloId)
                .map(leccionService::listLeccionesByModuloId)
                .map(lecciones -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de lecciones por módulo", lecciones)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Lista lecciones por curso
     * Implementado con programación funcional
     */
    @GetMapping("/curso/{cursoId}")
    public ResponseEntity<GenericResponse<List<LeccionDTO>>> listLeccionesByCursoId(@PathVariable Long cursoId) {
        return Optional.ofNullable(cursoId)
                .map(leccionService::listLeccionesByCursoId)
                .map(lecciones -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de lecciones por curso", lecciones)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una lección por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<LeccionDTO>> getLeccion(@PathVariable Long id) {
        return Optional.ofNullable(id)
                .map(leccionService::getLeccionById)
                .map(leccion -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lección encontrada", leccion)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza una lección existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<LeccionDTO>> updateLeccion(@PathVariable Long id, @Valid @RequestBody LeccionUpdateDTO dto) {
        return Optional.ofNullable(dto)
                .map(updateDto -> leccionService.updateLeccion(id, updateDto))
                .map(leccion -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lección actualizada exitosamente", leccion)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina una lección por su ID
     * Implementado con manejo explícito de excepciones
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deleteLeccion(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        try {
            leccionService.deleteLeccion(id);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lección eliminada exitosamente", null)
            );
        } catch (MidasExceptions ex) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, ex.getMessage(), null)
            );
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Error al eliminar la lección: " + ex.getMessage(), null));
        }
    }

    /**
     * Obtiene los subtítulos de una lección por su ID
     * Implementado con programación funcional
     * @param id ID de la lección
     * @return ResponseEntity con el contenido de los subtítulos
     */
    @GetMapping("/{id}/subtitulos")
    public ResponseEntity<byte[]> getSubtitulos(@PathVariable Long id) {
        try {
            // Buscar la lección en la base de datos
            Optional<Leccion> leccionOpt = leccionRepository.findById(id);
            if (leccionOpt.isEmpty()) {
                log.error("Lección no encontrada con ID: {}", id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }

            // Usar programación funcional para procesar la lección
            Leccion leccion = leccionOpt.get();
            String subtitlesUrl = leccion.getSubtitlesUrl();

            // Verificar si la lección tiene URL de subtítulos
            if (subtitlesUrl == null || subtitlesUrl.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            log.info("Descargando subtítulos desde: {}", subtitlesUrl);

            // Procesar la URL usando programación funcional
            String finalUrl = Optional.of(subtitlesUrl)
                    .map(url -> {
                        if (!url.contains("alt=media")) {
                            return url.contains("?") ? url + "&alt=media" : url + "?alt=media";
                        }
                        return url;
                    })
                    .orElse(subtitlesUrl);

            // Usar RestTemplate para descargar el contenido
            byte[] subtitlesContent = restTemplate.getForObject(new URI(finalUrl), byte[].class);

            // Configurar los headers de la respuesta
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.valueOf("text/vtt"));
            headers.setContentDispositionFormData("attachment", "subtitles.vtt");

            // Devolver los subtítulos
            return new ResponseEntity<>(subtitlesContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error al descargar los subtítulos de la lección {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
