// Estilos para el diálogo de colas de transcripción
.transcription-queue-dialog {
  .mat-dialog-container {
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
  }

  .mat-dialog-content {
    margin: 0;
    padding: 0;
    max-height: none;
  }
}

// Estilos para la tabla
.mat-table {
  .mat-header-cell {
    @apply bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-semibold text-xs uppercase tracking-wider;
    border-bottom: 1px solid theme('colors.gray.200');
    
    .dark & {
      border-bottom-color: theme('colors.gray.700');
    }
  }

  .mat-cell {
    @apply text-gray-900 dark:text-gray-100;
    border-bottom: 1px solid theme('colors.gray.100');
    
    .dark & {
      border-bottom-color: theme('colors.gray.800');
    }
  }

  .mat-row {
    &:hover {
      @apply bg-gray-50 dark:bg-gray-800;
    }
  }
}

// Estilos para los form fields
.mat-form-field {
  &.mat-form-field-appearance-outline {
    .mat-form-field-outline {
      @apply border-gray-300 dark:border-gray-600;
    }

    &.mat-focused .mat-form-field-outline-thick {
      @apply border-blue-500;
    }

    .mat-form-field-label {
      @apply text-gray-600 dark:text-gray-400;
    }

    &.mat-focused .mat-form-field-label {
      @apply text-blue-500;
    }
  }
}

// Estilos para los botones
.mat-raised-button {
  &.mat-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);

    &:hover {
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    }

    &:disabled {
      @apply bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400;
      box-shadow: none;
    }
  }
}

// Estilos para las tarjetas de estadísticas
.stats-card {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// Estilos para el spinner
.mat-spinner {
  svg circle {
    stroke: theme('colors.blue.500');
  }
}

// Estilos para la barra de progreso
.mat-progress-bar {
  .mat-progress-bar-fill::after {
    background-color: theme('colors.blue.500');
  }

  .mat-progress-bar-buffer {
    background-color: theme('colors.blue.200');
  }
}

// Estilos para los checkboxes
.mat-checkbox {
  .mat-checkbox-frame {
    @apply border-gray-400 dark:border-gray-500;
  }

  &.mat-checkbox-checked .mat-checkbox-background {
    @apply bg-blue-500;
  }

  .mat-checkbox-ripple .mat-ripple-element {
    background-color: theme('colors.blue.500');
  }
}

// Estilos para los snackbars
::ng-deep {
  .success-snackbar {
    .mat-snack-bar-container {
      @apply bg-green-600 text-white;
    }
  }

  .error-snackbar {
    .mat-snack-bar-container {
      @apply bg-red-600 text-white;
    }
  }

  .warning-snackbar {
    .mat-snack-bar-container {
      @apply bg-orange-600 text-white;
    }
  }
}

// Animaciones
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// Responsive
@media (max-width: 768px) {
  .mat-dialog-container {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }

  .grid {
    &.grid-cols-1.md\\:grid-cols-4 {
      grid-template-columns: repeat(2, 1fr);
    }

    &.grid-cols-1.md\\:grid-cols-3 {
      grid-template-columns: 1fr;
    }
  }

  .mat-table {
    font-size: 12px;

    .mat-header-cell,
    .mat-cell {
      padding: 8px 4px;
    }
  }
}

// Modo oscuro específico
.dark {
  .mat-dialog-container {
    @apply bg-gray-900 text-white;
  }

  .mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: theme('colors.gray.600');
    }

    .mat-form-field-infix {
      @apply text-white;
    }
  }

  .mat-table {
    @apply bg-gray-900;
  }
}
