package com.midas.crm.service.serviceImpl;

import com.midas.crm.config.RabbitMQConfig;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.service.AudioConversionService;
import com.midas.crm.service.TranscriptionQueueService;
import com.midas.crm.service.serviceImpl.GoogleDriveServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Qualifier;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Implementación del servicio de colas de transcripción
 */
@Service
@Slf4j
public class TranscriptionQueueServiceImpl implements TranscriptionQueueService {

    private final ClienteResidencialRepository clienteResidencialRepository;
    private final RabbitTemplate rabbitTemplate;
    private final AudioConversionService audioConversionService;
    private final GoogleDriveServiceImpl googleDriveService;
    private final RestTemplate restTemplate;
    private final ClienteResidencialService clienteResidencialService;

    public TranscriptionQueueServiceImpl(ClienteResidencialRepository clienteResidencialRepository,
                                         RabbitTemplate rabbitTemplate,
                                         AudioConversionService audioConversionService,
                                         GoogleDriveServiceImpl googleDriveService,
                                         @Qualifier("sslIgnoringRestTemplate") RestTemplate restTemplate,
                                         ClienteResidencialService clienteResidencialService) {
        this.clienteResidencialRepository = clienteResidencialRepository;
        this.rabbitTemplate = rabbitTemplate;
        this.audioConversionService = audioConversionService;
        this.googleDriveService = googleDriveService;
        this.restTemplate = restTemplate;
        this.clienteResidencialService = clienteResidencialService;
    }

    // Control de estado del procesamiento
    private final AtomicBoolean processingActive = new AtomicBoolean(true);

    // URLs de las APIs externas
    @Value("${transcription.api.url:https://apisozarusac.com/BackendTranscriptor/api}")
    private String transcriptionApiUrl;

    @Value("${comparison.api.url:https://apisozarusac.com/ventas/api}")
    private String comparisonApiUrl;

    @Value("${google.drive.api.url:http://localhost:9039/api/google-drive}")
    private String googleDriveApiUrl;

    @Value("${transcription.timeout.seconds:500}")
    private int transcriptionTimeoutSeconds;

    @Override
    public Map<String, Object> processPendingLeads(int batchSize, String numeroAgente) {
        log.info("Procesando leads pendientes. BatchSize: {}, NumeroAgente: {}", batchSize, numeroAgente);

        Map<String, Object> result = new HashMap<>();

        try {
            // Obtener leads pendientes
            List<ClienteResidencial> pendingLeads = getPendingLeadsFromDatabase(batchSize, numeroAgente);

            int totalFound = pendingLeads.size();
            int sentToQueue = 0;
            int errors = 0;
            List<String> errorMessages = new ArrayList<>();

            for (ClienteResidencial lead : pendingLeads) {
                try {
                    if (sendLeadToTranscriptionQueue(lead)) {
                        sentToQueue++;
                    } else {
                        errors++;
                        errorMessages.add("Error al enviar lead ID: " + lead.getId());
                    }
                } catch (Exception e) {
                    errors++;
                    errorMessages.add("Error al procesar lead ID " + lead.getId() + ": " + e.getMessage());
                    log.error("Error al procesar lead {}", lead.getId(), e);
                }
            }

            result.put("totalFound", totalFound);
            result.put("sentToQueue", sentToQueue);
            result.put("errors", errors);
            result.put("errorMessages", errorMessages);
            result.put("timestamp", LocalDateTime.now());

            log.info("Procesamiento completado. Total: {}, Enviados: {}, Errores: {}",
                    totalFound, sentToQueue, errors);

        } catch (Exception e) {
            log.error("Error general al procesar leads pendientes", e);
            result.put("error", "Error general: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getQueueStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // Obtener estadísticas de la base de datos
            long totalLeads = clienteResidencialRepository.count();
            long leadsWithTranscription = clienteResidencialRepository.countByTextoTranscriptionIsNotNull();
            long leadsWithoutTranscription = totalLeads - leadsWithTranscription;
            long leadsWithNotes = clienteResidencialRepository.countByNotaAgenteComparadorIAIsNotNull();

            stats.put("totalLeads", totalLeads);
            stats.put("leadsWithTranscription", leadsWithTranscription);
            stats.put("leadsWithoutTranscription", leadsWithoutTranscription);
            stats.put("leadsWithNotes", leadsWithNotes);
            stats.put("processingActive", processingActive.get());
            stats.put("timestamp", LocalDateTime.now());

            // Estadísticas por agente
            List<Object[]> agentStats = clienteResidencialRepository.getTranscriptionStatsByAgent();
            Map<String, Map<String, Long>> agentStatsMap = new HashMap<>();

            for (Object[] stat : agentStats) {
                String agente = (String) stat[0];
                Long total = (Long) stat[1];
                Long withTranscription = (Long) stat[2];

                Map<String, Long> agentData = new HashMap<>();
                agentData.put("total", total);
                agentData.put("withTranscription", withTranscription);
                agentData.put("withoutTranscription", total - withTranscription);

                agentStatsMap.put(agente != null ? agente : "SIN_AGENTE", agentData);
            }

            stats.put("statsByAgent", agentStatsMap);

        } catch (Exception e) {
            log.error("Error al obtener estadísticas de cola", e);
            stats.put("error", "Error al obtener estadísticas: " + e.getMessage());
        }

        return stats;
    }

    @Override
    public List<TranscriptionQueueMessage> getPendingLeads(int limit, String numeroAgente) {
        List<ClienteResidencial> pendingLeads = getPendingLeadsFromDatabase(limit, numeroAgente);

        return pendingLeads.stream()
                .map(this::convertToQueueMessage)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> retryFailedMessages(int maxRetries) {
        Map<String, Object> result = new HashMap<>();

        // TODO: Implementar lógica para obtener mensajes de la DLQ y reenviarlos
        // Por ahora, retornamos estadísticas básicas

        result.put("message", "Funcionalidad de reintento en desarrollo");
        result.put("maxRetries", maxRetries);
        result.put("timestamp", LocalDateTime.now());

        return result;
    }

    @Override
    public boolean sendLeadToQueue(Long leadId) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(leadId);

            if (leadOpt.isPresent()) {
                return sendLeadToTranscriptionQueue(leadOpt.get());
            } else {
                log.warn("Lead con ID {} no encontrado", leadId);
                return false;
            }

        } catch (Exception e) {
            log.error("Error al enviar lead {} a la cola", leadId, e);
            return false;
        }
    }

    @Override
    public void pauseProcessing() {
        processingActive.set(false);
        log.info("Procesamiento de colas pausado");
    }

    @Override
    public void resumeProcessing() {
        processingActive.set(true);
        log.info("Procesamiento de colas reanudado");
    }

    @Override
    public String normalizeAgentNumber(String numeroAgente) {
        if (numeroAgente == null || numeroAgente.trim().isEmpty()) {
            return null;
        }

        String agente = numeroAgente.trim().toLowerCase();

        // Remover prefijo "agen" si existe
        if (agente.startsWith("agen")) {
            agente = agente.substring(4);
        }

        // Remover ceros a la izquierda
        agente = agente.replaceFirst("^0+", "");

        // Si quedó vacío después de remover ceros, devolver "0"
        if (agente.isEmpty()) {
            agente = "0";
        }

        // Validar que solo contenga números
        if (!agente.matches("\\d+")) {
            return numeroAgente; // Devolver original si no es numérico
        }

        return agente;
    }

    @Override
    public boolean isLeadEligibleForProcessing(Long leadId) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(leadId);

            if (leadOpt.isPresent()) {
                ClienteResidencial lead = leadOpt.get();

                // Verificar que tenga número móvil
                if (lead.getMovilContacto() == null || lead.getMovilContacto().trim().isEmpty()) {
                    return false;
                }

                // Verificar que no tenga transcripción
                if (lead.getTextoTranscription() != null && !lead.getTextoTranscription().trim().isEmpty()) {
                    return false;
                }

                // Verificar que tenga número de agente
                if (lead.getNumeroAgente() == null || lead.getNumeroAgente().trim().isEmpty()) {
                    return false;
                }

                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("Error al verificar elegibilidad del lead {}", leadId, e);
            return false;
        }
    }

    @Override
    public boolean isProcessingActive() {
        return processingActive.get();
    }

    // ========== MÉTODOS PRIVADOS ==========

    /**
     * Obtiene leads pendientes de la base de datos
     */
    private List<ClienteResidencial> getPendingLeadsFromDatabase(int limit, String numeroAgente) {
        try {
            if (numeroAgente != null && !numeroAgente.trim().isEmpty()) {
                String normalizedAgent = normalizeAgentNumber(numeroAgente);
                return clienteResidencialRepository.findLeadsWithoutTranscriptionByAgent(normalizedAgent, limit);
            } else {
                return clienteResidencialRepository.findLeadsWithoutTranscription(limit);
            }
        } catch (Exception e) {
            log.error("Error al obtener leads pendientes de la base de datos", e);
            return new ArrayList<>();
        }
    }

    /**
     * Envía un lead a la cola de transcripción
     */
    private boolean sendLeadToTranscriptionQueue(ClienteResidencial lead) {
        try {
            TranscriptionQueueMessage message = convertToQueueMessage(lead);

            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                    RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                    message
            );

            log.debug("Lead {} enviado a la cola de transcripción", lead.getId());
            return true;

        } catch (Exception e) {
            log.error("Error al enviar lead {} a la cola de transcripción", lead.getId(), e);
            return false;
        }
    }

    /**
     * Convierte un ClienteResidencial a TranscriptionQueueMessage
     */
    private TranscriptionQueueMessage convertToQueueMessage(ClienteResidencial lead) {
        String numeroAgenteNormalizado = normalizeAgentNumber(lead.getNumeroAgente());

        return TranscriptionQueueMessage.builder()
                .leadId(lead.getId())
                .numeroMovil(lead.getMovilContacto())
                .numeroAgente(lead.getNumeroAgente())
                .numeroAgenteNormalizado(numeroAgenteNormalizado)
                .nombreCliente(lead.getNombresApellidos())
                .dniCliente(lead.getNifNie())
                .fechaCreacion(lead.getFechaCreacion())
                .nombreAsesor(lead.getUsuario() != null ? lead.getUsuario().getNombres() : null)
                .archivoMp3Url(null) // Se establecerá cuando se obtenga de Google Drive
                .nombreArchivoMp3(lead.getNombreArchivoMp3())
                .estadoProcesamiento("PENDING")
                .fechaEnvio(LocalDateTime.now())
                .intentos(0)
                .maxIntentos(3)
                .queueName(RabbitMQConfig.TRANSCRIPTION_QUEUE)
                .routingKey(RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY)
                // 🔧 VALORES POR DEFECTO PARA TRANSCRIPCIÓN
                .whisperModel("base")
                .device("cpu")
                .targetLanguage("es")
                .callType("inbound")
                .callId(generateCallId(lead.getMovilContacto(), lead.getFechaCreacion()))
                .agentId(numeroAgenteNormalizado)
                .callDatetime(lead.getFechaCreacion().toString())
                .build();
    }

    /**
     * Genera un ID único para la llamada
     */
    private String generateCallId(String numeroMovil, LocalDateTime fechaCreacion) {
        String timestamp = fechaCreacion.toString().replaceAll("[^0-9]", "");
        String random = String.valueOf((int)(Math.random() * 10000));
        String phone = numeroMovil != null && numeroMovil.length() >= 4
                ? numeroMovil.substring(numeroMovil.length() - 4)
                : "0000";

        return String.format("CALL_%s_%s_%s", timestamp, phone, random);
    }

    // ========== LISTENERS DE RABBITMQ ==========

    /**
     * Procesa mensajes de la cola de transcripción
     */
    @RabbitListener(queues = RabbitMQConfig.TRANSCRIPTION_QUEUE)
    public void processTranscriptionMessage(TranscriptionQueueMessage message) {
        if (!processingActive.get()) {
            log.info("Procesamiento pausado, reenvíando mensaje a la cola");
            // Reenviar el mensaje a la cola para procesarlo más tarde
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                    RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                    message
            );
            return;
        }

        log.info("Procesando mensaje de transcripción para lead ID: {}", message.getLeadId());

        try {
            message.marcarComoProcesando();
            message.incrementarIntentos();

            // 1. Buscar archivo de audio en Google Drive (GSM o MP3)
            String audioUrl = findMp3FileInGoogleDrive(message);
            if (audioUrl == null) {
                throw new RuntimeException("No se encontró archivo de audio para el lead");
            }

            // 2. Procesar archivo (convertir GSM a MP3 si es necesario)
            String mp3Url = processAudioFile(audioUrl, message);
            message.setArchivoMp3Url(mp3Url);

            // 3. Llamar a la API de transcripción
            Map<String, Object> transcriptionResult = callTranscriptionAPI(message);

            // 4. Guardar resultado en la base de datos
            saveTranscriptionResult(message, transcriptionResult);

            // 5. Ejecutar automáticamente el comparador
            executeComparison(message, transcriptionResult);

            message.marcarComoCompletado();
            log.info("Transcripción completada para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al procesar transcripción para lead ID: {}", message.getLeadId(), e);

            message.marcarComoFallido(e.getMessage());

            if (!message.intentosAgotados()) {
                // Reenviar a la cola para reintento
                log.info("Reenviando mensaje para reintento. Intento: {}/{}",
                        message.getIntentos(), message.getMaxIntentos());

                rabbitTemplate.convertAndSend(
                        RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                        RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                        message
                );
            } else {
                log.error("Intentos agotados para lead ID: {}. Enviando a DLQ", message.getLeadId());
                // El mensaje irá automáticamente a la DLQ por configuración
            }
        }
    }

    /**
     * Procesa mensajes de la cola de comparación
     */
    @RabbitListener(queues = RabbitMQConfig.COMPARISON_QUEUE)
    public void processComparisonMessage(TranscriptionQueueMessage message) {
        if (!processingActive.get()) {
            log.info("Procesamiento pausado, reenvíando mensaje de comparación a la cola");
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.COMPARISON_EXCHANGE,
                    RabbitMQConfig.COMPARISON_ROUTING_KEY,
                    message
            );
            return;
        }

        log.info("Procesando mensaje de comparación para lead ID: {}", message.getLeadId());

        try {
            message.incrementarIntentos();

            // Llamar a la API de comparación
            Map<String, Object> comparisonResult = callComparisonAPI(message);

            // Guardar resultado de comparación
            saveComparisonResult(message, comparisonResult);

            log.info("Comparación completada para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al procesar comparación para lead ID: {}", message.getLeadId(), e);

            if (!message.intentosAgotados()) {
                log.info("Reenviando mensaje de comparación para reintento. Intento: {}/{}",
                        message.getIntentos(), message.getMaxIntentos());

                rabbitTemplate.convertAndSend(
                        RabbitMQConfig.COMPARISON_EXCHANGE,
                        RabbitMQConfig.COMPARISON_ROUTING_KEY,
                        message
                );
            } else {
                log.error("Intentos agotados para comparación de lead ID: {}", message.getLeadId());
            }
        }
    }

    // ========== MÉTODOS DE INTEGRACIÓN CON APIS EXTERNAS ==========

    /**
     * Busca archivo de audio en Google Drive basado en el número móvil y agente
     */
    private String findMp3FileInGoogleDrive(TranscriptionQueueMessage message) {
        try {
            String numeroAgenteNormalizado = message.getNumeroAgenteNormalizado();
            String numeroMovil = message.getNumeroMovil();

            if (numeroAgenteNormalizado == null || numeroAgenteNormalizado.trim().isEmpty()) {
                log.warn("Número de agente normalizado vacío para lead ID: {}", message.getLeadId());
                return null;
            }

            log.info("Buscando archivo de audio para móvil {} y agente {} (lead ID: {})",
                    numeroMovil, numeroAgenteNormalizado, message.getLeadId());

            // Buscar archivo de audio usando el servicio de Google Drive directamente
            // Primero intentar búsqueda precisa por móvil y agente
            if (numeroMovil != null && !numeroMovil.trim().isEmpty()) {
                try {
                    // Llamar directamente al servicio de Google Drive
                    String audioUrl = googleDriveService.findAudioByMovilAndAgent(numeroMovil, numeroAgenteNormalizado, null);
                    if (audioUrl != null) {
                        log.info("Archivo de audio encontrado por móvil y agente: {}", audioUrl);

                        // Verificar si es GSM y convertir a MP3 si es necesario
                        // Nota: Asumimos que todos los archivos encontrados son GSM ya que vienen de las grabaciones
                        return processAudioFileAsGsm(audioUrl, message);
                    }
                } catch (Exception e) {
                    log.warn("Error al buscar por móvil y agente: {}", e.getMessage());
                }
            }

            // Si no se encuentra por móvil, buscar solo por agente
            try {
                String audioUrl = googleDriveService.findMp3UrlByAgent(numeroAgenteNormalizado, null);
                if (audioUrl != null) {
                    log.info("Archivo de audio encontrado para agente {}: {}", numeroAgenteNormalizado, audioUrl);

                    // Verificar si es GSM y convertir a MP3 si es necesario
                    // Nota: Asumimos que todos los archivos encontrados son GSM ya que vienen de las grabaciones
                    return processAudioFileAsGsm(audioUrl, message);
                }
            } catch (Exception e) {
                log.warn("Error al buscar por agente: {}", e.getMessage());
            }

            log.warn("No se encontró archivo de audio para móvil {} y agente {} (lead ID: {})",
                    numeroMovil, numeroAgenteNormalizado, message.getLeadId());
            return null;

        } catch (Exception e) {
            log.error("Error al buscar archivo de audio en Google Drive para lead ID: {}", message.getLeadId(), e);
            return null;
        }
    }

    /**
     * Procesa un archivo de audio, convirtiendo de GSM a MP3 si es necesario
     */
    private String processAudioFile(String audioUrl, TranscriptionQueueMessage message) {
        try {
            log.info("Procesando archivo de audio: {}", audioUrl);

            // Verificar si el archivo es GSM basándose en la URL o extensión
            if (isGsmFile(audioUrl)) {
                log.info("Archivo GSM detectado, iniciando conversión a MP3");

                // Generar nombre único para el archivo MP3 convertido
                String outputFileName = generateMp3FileName(message);

                // Convertir GSM a MP3 usando el servicio de conversión
                String mp3Url = audioConversionService.convertGsmToMp3(audioUrl, outputFileName);

                log.info("Conversión GSM a MP3 completada. URL del MP3: {}", mp3Url);
                return mp3Url;

            } else {
                log.info("Archivo ya es MP3 o formato compatible, usando directamente");
                return audioUrl;
            }

        } catch (Exception e) {
            log.error("Error al procesar archivo de audio para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al procesar archivo de audio: " + e.getMessage(), e);
        }
    }

    /**
     * Procesa un archivo de audio asumiendo que es GSM (para archivos de grabaciones)
     */
    private String processAudioFileAsGsm(String audioUrl, TranscriptionQueueMessage message) {
        try {
            log.info("Procesando archivo de audio como GSM: {}", audioUrl);

            // Verificar si FFmpeg está disponible
            if (!audioConversionService.isFFmpegAvailable()) {
                log.warn("FFmpeg no está disponible. Enviando archivo GSM directamente al API de transcripción");
                log.warn("NOTA: Para mejor calidad de transcripción, instale FFmpeg para conversión a MP3");
                return audioUrl; // Enviar GSM directamente como fallback
            }

            log.info("Archivo GSM detectado (por contexto de grabación), iniciando conversión a MP3");

            // Generar nombre único para el archivo MP3 convertido
            String outputFileName = generateMp3FileName(message);

            // Convertir GSM a MP3 usando el servicio de conversión
            String mp3Url = audioConversionService.convertGsmToMp3(audioUrl, outputFileName);

            log.info("Conversión GSM a MP3 completada. URL del MP3: {}", mp3Url);
            return mp3Url;

        } catch (Exception e) {
            log.error("Error al procesar archivo de audio GSM para lead ID: {}", message.getLeadId(), e);
            log.warn("Fallback: Enviando archivo GSM original al API de transcripción");
            return audioUrl; // Fallback en caso de error
        }
    }

    /**
     * Verifica si un archivo es GSM basándose en la URL
     */
    private boolean isGsmFile(String audioUrl) {
        if (audioUrl == null) {
            return false;
        }

        String urlLower = audioUrl.toLowerCase();
        boolean isGsm = urlLower.contains(".gsm") || urlLower.contains("audio/x-gsm") || urlLower.contains("mimeType='audio/x-gsm'");

        log.debug("Verificando tipo de archivo. URL: {}, Es GSM: {}", audioUrl, isGsm);
        return isGsm;
    }

    /**
     * Genera un nombre único para el archivo MP3 convertido
     */
    private String generateMp3FileName(TranscriptionQueueMessage message) {
        StringBuilder fileName = new StringBuilder();

        // Agregar fecha actual
        LocalDateTime now = LocalDateTime.now();
        fileName.append(now.format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));

        // Agregar número móvil si está disponible
        if (message.getNumeroMovil() != null && !message.getNumeroMovil().trim().isEmpty()) {
            String cleanMovil = message.getNumeroMovil().replaceAll("[^0-9]", "");
            fileName.append("_").append(cleanMovil);
        }

        // Agregar agente
        if (message.getNumeroAgenteNormalizado() != null) {
            fileName.append("_agent").append(String.format("%03d", Integer.parseInt(message.getNumeroAgenteNormalizado())));
        }

        // Agregar ID del lead
        fileName.append("_lead").append(message.getLeadId());

        // Agregar sufijo para indicar que es convertido
        fileName.append("_converted");

        return fileName.toString();
    }



    /**
     * Llama a la API de transcripción externa
     */
    private Map<String, Object> callTranscriptionAPI(TranscriptionQueueMessage message) {
        try {
            log.info("Enviando solicitud de transcripción para lead ID: {} con URL: {}",
                    message.getLeadId(), message.getArchivoMp3Url());

            // 1. Descargar el archivo MP3 desde Google Drive
            log.info("Descargando archivo MP3 para envío al API de transcripción");
            byte[] audioFileBytes = restTemplate.getForObject(message.getArchivoMp3Url(), byte[].class);

            if (audioFileBytes == null || audioFileBytes.length == 0) {
                throw new RuntimeException("No se pudo descargar el archivo de audio desde: " + message.getArchivoMp3Url());
            }

            log.info("Archivo MP3 descargado exitosamente. Tamaño: {} bytes", audioFileBytes.length);

            // 2. Preparar headers para multipart/form-data
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 3. Crear el recurso del archivo
            ByteArrayResource audioResource = new ByteArrayResource(audioFileBytes) {
                @Override
                public String getFilename() {
                    return "audio.mp3"; // Nombre del archivo para el API
                }
            };

            // 4. Preparar datos como multipart form
            MultiValueMap<String, Object> requestData = new LinkedMultiValueMap<>();
            requestData.add("audio_file", audioResource);
            requestData.add("whisper_model", message.getWhisperModel());
            requestData.add("device", message.getDevice());
            requestData.add("target_language", message.getTargetLanguage());
            requestData.add("call_type", message.getCallType());
            requestData.add("call_id", message.getCallId());
            requestData.add("caller_phone", message.getNumeroMovil());
            requestData.add("agent_id", message.getAgentId());
            requestData.add("call_datetime", message.getCallDatetime());

            // 📋 LOGS DETALLADOS DE LO QUE SE ENVÍA AL API
            log.info("=== 📋 DATOS ENVIADOS AL API DE TRANSCRIPCIÓN ===");
            log.info("🎯 Lead ID: {}", message.getLeadId());
            log.info("📞 Número móvil: {}", message.getNumeroMovil());
            log.info("👤 Agent ID: {}", message.getAgentId());
            log.info("🔧 Whisper Model: {}", message.getWhisperModel());
            log.info("💻 Device: {}", message.getDevice());
            log.info("🌐 Target Language: {}", message.getTargetLanguage());
            log.info("📋 Call Type: {}", message.getCallType());
            log.info("🆔 Call ID: {}", message.getCallId());
            log.info("📅 Call DateTime: {}", message.getCallDatetime());
            log.info("🎵 Audio File Size: {} bytes ({} MB)", audioFileBytes.length, audioFileBytes.length / 1024.0 / 1024.0);
            log.info("🔗 Audio URL Original: {}", message.getArchivoMp3Url());
            log.info("📡 API URL: {}", transcriptionApiUrl + "/transcribe/");
            log.info("⏱️ Timeout configurado: {} segundos", transcriptionTimeoutSeconds);
            log.info("================================================");

            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(requestData, headers);

            // 5. Llamar a la API con timeout
            String url = transcriptionApiUrl + "/transcribe/";
            log.info("Enviando POST a: {} con Content-Type: {} y archivo de {} bytes",
                    url, headers.getContentType(), audioFileBytes.length);

            // Configurar timeout específico para transcripción (puede tomar tiempo)
            long startTime = System.currentTimeMillis();
            log.info("Iniciando llamada al API de transcripción. Timeout configurado: {} segundos", transcriptionTimeoutSeconds);

            try {
                // 🚀 SISTEMA ASÍNCRONO PROFESIONAL CON MONITOREO
                log.info("🚀 Iniciando petición asíncrona con monitoreo de progreso...");

                CompletableFuture<ResponseEntity<Map>> futureResponse = CompletableFuture.supplyAsync(() -> {
                    try {
                        return restTemplate.postForEntity(url, entity, Map.class);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });

                // Monitoreo de progreso cada 30 segundos
                ResponseEntity<Map> response = monitorTranscriptionProgress(futureResponse, startTime, message.getLeadId());

                long duration = System.currentTimeMillis() - startTime;
                log.info("✅ API de transcripción respondió exitosamente en {} ms ({} segundos)",
                        duration, duration / 1000.0);

                return processTranscriptionResponse(response, message);

            } catch (org.springframework.web.client.ResourceAccessException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("❌ TIMEOUT en API de transcripción después de {} ms ({} segundos). Error: {}",
                        duration, duration / 1000.0, e.getMessage());
                throw new RuntimeException("Timeout en API de transcripción después de " + (duration / 1000.0) + " segundos", e);

            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("❌ Error en API de transcripción después de {} ms ({} segundos): {}",
                        duration, duration / 1000.0, e.getMessage());
                throw e;
            }

        } catch (Exception e) {
            log.error("Error al llamar API de transcripción para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error en API de transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Monitorea el progreso de la transcripción con logs periódicos
     */
    private ResponseEntity<Map> monitorTranscriptionProgress(CompletableFuture<ResponseEntity<Map>> futureResponse,
                                                           long startTime, Long leadId) {
        try {
            long maxWaitTime = transcriptionTimeoutSeconds * 1000L; // Convertir a milisegundos
            long checkInterval = 30000L; // 30 segundos
            long nextLogTime = startTime + checkInterval;

            while (!futureResponse.isDone()) {
                long currentTime = System.currentTimeMillis();
                long elapsed = currentTime - startTime;

                // Verificar timeout
                if (elapsed > maxWaitTime) {
                    futureResponse.cancel(true);
                    throw new RuntimeException("Timeout después de " + (elapsed / 1000.0) + " segundos");
                }

                // Log de progreso cada 30 segundos
                if (currentTime >= nextLogTime) {
                    double elapsedSeconds = elapsed / 1000.0;
                    double remainingSeconds = (maxWaitTime - elapsed) / 1000.0;
                    log.info("⏳ Transcripción en progreso para lead {}: {:.1f}s transcurridos, {:.1f}s restantes",
                            leadId, elapsedSeconds, remainingSeconds);
                    nextLogTime = currentTime + checkInterval;
                }

                // Esperar un poco antes de verificar de nuevo
                Thread.sleep(1000); // 1 segundo
            }

            // Obtener el resultado
            return futureResponse.get();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            futureResponse.cancel(true);
            throw new RuntimeException("Proceso interrumpido", e);
        } catch (Exception e) {
            futureResponse.cancel(true);
            throw new RuntimeException("Error en monitoreo de transcripción", e);
        }
    }

    /**
     * Procesa la respuesta del API de transcripción
     */
    private Map<String, Object> processTranscriptionResponse(ResponseEntity<Map> response, TranscriptionQueueMessage message) {
        // 📥 LOGS DETALLADOS DE LA RESPUESTA
        log.info("=== 📥 RESPUESTA DEL API DE TRANSCRIPCIÓN ===");
        log.info("🎯 Lead ID: {}", message.getLeadId());
        log.info("📊 Status Code: {}", response.getStatusCode());
        log.info("📋 Headers de respuesta: {}", response.getHeaders());

        if ((response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) && response.getBody() != null) {
            Map<String, Object> responseBody = response.getBody();

            // ✅ LOG DE ÉXITO
            log.info("✅ API de transcripción respondió exitosamente con código: {}", response.getStatusCode());

            // 📄 LOG COMPLETO DEL BODY
            log.info("📄 Body completo de respuesta:");
            log.info("   📦 Tamaño del body: {} campos", responseBody.size());
            log.info("   🔑 Claves disponibles: {}", responseBody.keySet());

            // 📝 LOG DETALLADO DE CADA CAMPO
            for (Map.Entry<String, Object> entry : responseBody.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                String valueType = value != null ? value.getClass().getSimpleName() : "null";
                String valuePreview = value != null ?
                        (value.toString().length() > 100 ? value.toString().substring(0, 100) + "..." : value.toString())
                        : "null";
                log.info("   📝 {}: ({}) {}", key, valueType, valuePreview);
            }

            // Verificar si la respuesta contiene el texto de transcripción
            if (responseBody.containsKey("transcription") || responseBody.containsKey("text")) {
                String transcriptionText = (String) (responseBody.containsKey("transcription") ?
                        responseBody.get("transcription") : responseBody.get("text"));
                log.info("✅ Transcripción encontrada exitosamente para lead ID: {}", message.getLeadId());
                log.info("📝 Longitud del texto transcrito: {} caracteres",
                        transcriptionText != null ? transcriptionText.length() : 0);
                log.info("📄 Primeros 200 caracteres: {}",
                        transcriptionText != null && transcriptionText.length() > 200 ?
                                transcriptionText.substring(0, 200) + "..." : transcriptionText);
                log.info("================================================");
                return responseBody;
            } else {
                log.warn("⚠️ Respuesta del API no contiene transcripción para lead ID: {}", message.getLeadId());
                log.warn("🔍 Claves esperadas: 'transcription' o 'text'");
                log.warn("🔑 Claves encontradas: {}", responseBody.keySet());
                log.info("================================================");
                return responseBody; // Devolver de todas formas para debug
            }
        } else {
            log.error("❌ API de transcripción retornó estado no exitoso: {}", response.getStatusCode());
            log.error("📄 Body de respuesta: {}", response.getBody());
            log.error("🔍 Códigos de éxito esperados: 200 OK, 201 CREATED");
            log.info("================================================");
            throw new RuntimeException("API de transcripción retornó estado: " + response.getStatusCode());
        }
    }

    /**
     * Llama a la API de comparación externa (simplificado)
     */
    @Transactional(readOnly = true)
    private Map<String, Object> callComparisonAPI(TranscriptionQueueMessage message) {
        try {
            // Obtener datos del lead para comparación
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para comparación");
            }

            ClienteResidencial lead = leadOpt.get();

            // Preparar headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 🎯 PREPARAR DATOS COMPLETOS PARA EL API COMPARADOR
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("texto_audio", lead.getTextoTranscription());

            // Estructura completa del lead como espera el API comparador
            Map<String, Object> datosLead = buildCompleteLeadStructure(lead);
            requestData.put("datos_lead", datosLead);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            // 📋 LOGS SIMPLIFICADOS
            log.info("=== 📋 DATOS ENVIADOS AL API COMPARADOR ===");
            log.info("🎯 Lead ID: {}", message.getLeadId());
            log.info("📡 API URL: {}", comparisonApiUrl + "/comparar/");
            log.info("🔤 texto_audio (longitud): {} caracteres",
                    lead.getTextoTranscription() != null ? lead.getTextoTranscription().length() : 0);
            log.info("👤 nombresApellidos: {}", lead.getNombresApellidos());
            log.info("📞 movilContacto: {}", lead.getMovilContacto());
            log.info("🎯 numeroAgente: {}", lead.getNumeroAgente());
            log.info("================================================");

            // Llamar a la API
            String url = comparisonApiUrl + "/comparar/";
            log.info("🚀 Enviando petición al API comparador...");
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new RuntimeException("API de comparación retornó estado: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error al llamar API de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error en API de comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Construye la estructura completa del lead para el API comparador
     */
    private Map<String, Object> buildCompleteLeadStructure(ClienteResidencial lead) {
        Map<String, Object> datosLead = new HashMap<>();

        // Datos básicos del lead
        datosLead.put("rpta", 1);
        datosLead.put("msg", "Cliente encontrado");
        datosLead.put("id", lead.getId());
        datosLead.put("campania", lead.getCampania());
        datosLead.put("nombresApellidos", lead.getNombresApellidos());
        datosLead.put("nifNie", lead.getNifNie());
        datosLead.put("permanencia", lead.getPermanencia());
        datosLead.put("direccion", lead.getDireccion());
        datosLead.put("movilContacto", lead.getMovilContacto());
        datosLead.put("planActual", lead.getPlanActual());
        datosLead.put("codigoPostal", lead.getCodigoPostal());
        datosLead.put("provincia", lead.getProvincia());
        datosLead.put("distrito", lead.getDistrito());
        datosLead.put("tipoPlan", lead.getTipoPlan());
        datosLead.put("fechaCreacion", lead.getFechaCreacion());
        datosLead.put("numeroAgente", lead.getNumeroAgente());
        datosLead.put("titularDelServicio", lead.getTitularDelServicio());
        datosLead.put("numeroMoviles", lead.getNumeroMoviles());
        datosLead.put("textoTranscription", lead.getTextoTranscription());
        datosLead.put("urlDriveTranscripcion", lead.getUrlDriveTranscripcion());
        datosLead.put("notaAgenteComparadorIA", lead.getNotaAgenteComparadorIA());
        datosLead.put("velocidad", lead.getVelocidad());
        datosLead.put("ventaRealizada", lead.getVentaRealizada());
        datosLead.put("autorizaSeguros", lead.getAutorizaSeguros());
        datosLead.put("autorizaEnergias", lead.getAutorizaEnergias());
        datosLead.put("deseaPromocionesLowi", lead.getDeseaPromocionesLowi());

        // Datos del usuario (estructura básica sin lazy loading)
        Map<String, Object> usuario = new HashMap<>();
        try {
            if (lead.getUsuario() != null) {
                usuario.put("id", lead.getUsuario().getId());
                usuario.put("username", null); // Evitar lazy loading
                usuario.put("password", null);
                usuario.put("nombre", null);
                usuario.put("apellido", null);
                usuario.put("dni", null);
                usuario.put("email", null);
                usuario.put("fechaCreacion", null);
                usuario.put("estado", true);
                usuario.put("role", null);

                // Coordinador (estructura básica)
                Map<String, Object> coordinador = new HashMap<>();
                coordinador.put("id", null);
                coordinador.put("username", null);
                coordinador.put("nombre", null);
                coordinador.put("apellido", null);
                coordinador.put("dni", null);
                coordinador.put("email", null);
                coordinador.put("fechaCreacion", null);
                coordinador.put("estado", true);
                coordinador.put("role", null);

                usuario.put("coordinador", coordinador);
            } else {
                // Usuario vacío si no existe
                usuario.put("id", null);
                usuario.put("username", null);
                usuario.put("password", null);
                usuario.put("nombre", null);
                usuario.put("apellido", null);
                usuario.put("dni", null);
                usuario.put("email", null);
                usuario.put("fechaCreacion", null);
                usuario.put("estado", true);
                usuario.put("role", null);
                usuario.put("coordinador", null);
            }
        } catch (Exception e) {
            log.warn("Error al acceder a datos del usuario para lead {}: {}", lead.getId(), e.getMessage());
            // Usuario con valores por defecto
            usuario.put("id", null);
            usuario.put("username", null);
            usuario.put("password", null);
            usuario.put("nombre", null);
            usuario.put("apellido", null);
            usuario.put("dni", null);
            usuario.put("email", null);
            usuario.put("fechaCreacion", null);
            usuario.put("estado", true);
            usuario.put("role", null);
            usuario.put("coordinador", null);
        }

        datosLead.put("usuario", usuario);

        log.debug("Estructura completa del lead construida para API comparador. Lead ID: {}", lead.getId());
        return datosLead;
    }

    /**
     * Guarda el resultado de la transcripción en la base de datos
     */
    private void saveTranscriptionResult(TranscriptionQueueMessage message, Map<String, Object> transcriptionResult) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para guardar transcripción");
            }

            ClienteResidencial lead = leadOpt.get();

            // Extraer texto de transcripción del resultado
            String transcriptionText = (String) transcriptionResult.get("transcription");
            if (transcriptionText != null) {
                lead.setTextoTranscription(transcriptionText);


            }

            // Guardar nombre del archivo MP3 (extraer de la URL)
            String nombreArchivoMp3 = extractMp3FileName(message.getArchivoMp3Url());
            if (nombreArchivoMp3 != null) {
                lead.setNombreArchivoMp3(nombreArchivoMp3);
                log.info("📁 Nombre del archivo MP3 guardado: {}", nombreArchivoMp3);
            }

            // Guardar URL de transcripción en Google Drive
            if (transcriptionText != null) {
                String driveUrl = saveTranscriptionToGoogleDrive(transcriptionText, message);
                if (driveUrl != null) {
                    lead.setUrlDriveTranscripcion(driveUrl);
                    log.info("☁️ URL de transcripción en Google Drive guardada: {}", driveUrl);
                }
            }

            clienteResidencialRepository.save(lead);
            log.info("Resultado de transcripción guardado para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al guardar resultado de transcripción para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al guardar transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Extrae el nombre del archivo MP3 desde la URL de Google Drive
     */
    private String extractMp3FileName(String mp3Url) {
        try {
            if (mp3Url == null || mp3Url.trim().isEmpty()) {
                return null;
            }

            // Si es una URL de Google Drive, extraer el ID y buscar el nombre del archivo
            if (mp3Url.contains("drive.google.com")) {
                String fileId = extractGoogleDriveFileId(mp3Url);
                if (fileId != null) {
                    // Buscar el nombre del archivo en Google Drive usando el ID
                    return googleDriveService.getFileName(fileId);
                }
            }

            // Si no es de Google Drive, extraer el nombre de la URL
            String[] urlParts = mp3Url.split("/");
            String fileName = urlParts[urlParts.length - 1];

            // Remover parámetros de query si existen
            if (fileName.contains("?")) {
                fileName = fileName.split("\\?")[0];
            }

            return fileName;

        } catch (Exception e) {
            log.warn("No se pudo extraer el nombre del archivo MP3 de la URL: {}", mp3Url, e);
            return null;
        }
    }

    /**
     * Extrae el ID del archivo de una URL de Google Drive
     */
    private String extractGoogleDriveFileId(String driveUrl) {
        try {
            if (driveUrl.contains("/file/d/")) {
                String[] parts = driveUrl.split("/file/d/");
                if (parts.length > 1) {
                    String idPart = parts[1];
                    if (idPart.contains("/")) {
                        return idPart.split("/")[0];
                    }
                    return idPart;
                }
            } else if (driveUrl.contains("id=")) {
                String[] parts = driveUrl.split("id=");
                if (parts.length > 1) {
                    String idPart = parts[1];
                    if (idPart.contains("&")) {
                        return idPart.split("&")[0];
                    }
                    return idPart;
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("No se pudo extraer el ID del archivo de Google Drive: {}", driveUrl, e);
            return null;
        }
    }

    /**
     * Guarda el texto de transcripción como archivo en Google Drive
     */
    private String saveTranscriptionToGoogleDrive(String transcriptionText, TranscriptionQueueMessage message) {
        try {
            log.info("Guardando transcripción en Google Drive para lead ID: {}", message.getLeadId());

            // Generar nombre del archivo
            String fileName = generateTranscriptionFileName(message);

            // Crear archivo temporal con el texto
            String tempFilePath = createTempTranscriptionFile(transcriptionText, fileName);

            // Subir a Google Drive en la carpeta TEXTOS_TRANSCRITOS
            String folderId = findOrCreateTranscriptionFolder();
            String fileId = googleDriveService.uploadFile(
                    createMultipartFileFromPath(tempFilePath, fileName + ".txt"),
                    fileName + ".txt",
                    folderId
            );

            // Generar URL de visualización
            String driveUrl = "https://drive.google.com/file/d/" + fileId + "/view";

            log.info("Transcripción guardada en Google Drive: {}", driveUrl);
            return driveUrl;

        } catch (Exception e) {
            log.error("Error al guardar transcripción en Google Drive para lead ID: {}", message.getLeadId(), e);
            return null; // No fallar el proceso principal si no se puede subir a Drive
        }
    }

    /**
     * Busca o crea la carpeta para transcripciones
     */
    private String findOrCreateTranscriptionFolder() throws IOException {
        String folderName = "TEXTOS_TRANSCRITOS";

        // Primero buscar si existe
        String existingFolderId = googleDriveService.findFolderByName(folderName);
        if (existingFolderId != null) {
            log.debug("Carpeta de transcripciones encontrada: {}", existingFolderId);
            return existingFolderId;
        }

        // Si no existe, crear nueva carpeta
        log.info("Creando carpeta de transcripciones: {}", folderName);
        String folderId = googleDriveService.createFolder(folderName);
        log.info("Carpeta de transcripciones creada: {}", folderId);

        return folderId;
    }

    /**
     * Genera nombre para el archivo de transcripción
     */
    private String generateTranscriptionFileName(TranscriptionQueueMessage message) {
        StringBuilder fileName = new StringBuilder();

        // Agregar fecha actual
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss");
        fileName.append(now.format(formatter));

        // Agregar número móvil
        if (message.getNumeroMovil() != null) {
            fileName.append("_").append(message.getNumeroMovil());
        }

        // Agregar agente
        if (message.getNumeroAgenteNormalizado() != null) {
            fileName.append("_agent").append(message.getNumeroAgenteNormalizado());
        }

        // Agregar lead ID
        fileName.append("_lead").append(message.getLeadId());

        // Agregar sufijo
        fileName.append("_transcription");

        return fileName.toString();
    }

    /**
     * Crea un archivo temporal con el texto de transcripción
     */
    private String createTempTranscriptionFile(String transcriptionText, String fileName) throws IOException {
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "transcriptions");
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        Path tempFile = tempDir.resolve(fileName + ".txt");
        Files.write(tempFile, transcriptionText.getBytes(StandardCharsets.UTF_8));

        return tempFile.toString();
    }

    /**
     * Crea un MultipartFile desde un archivo temporal
     */
    private MultipartFile createMultipartFileFromPath(String filePath, String fileName) throws IOException {
        Path path = Paths.get(filePath);
        byte[] content = Files.readAllBytes(path);

        return new MultipartFile() {
            @Override
            public String getName() { return "file"; }

            @Override
            public String getOriginalFilename() { return fileName; }

            @Override
            public String getContentType() { return "text/plain"; }

            @Override
            public boolean isEmpty() { return content.length == 0; }

            @Override
            public long getSize() { return content.length; }

            @Override
            public byte[] getBytes() { return content; }

            @Override
            public InputStream getInputStream() { return new ByteArrayInputStream(content); }

            @Override
            public void transferTo(File dest) throws IOException {
                Files.write(dest.toPath(), content);
            }
        };
    }

    /**
     * Guarda el resultado de la comparación en la base de datos
     */
    private void saveComparisonResult(TranscriptionQueueMessage message, Map<String, Object> comparisonResult) {
        try {
            // 📥 LOGS DETALLADOS DE LA RESPUESTA DEL COMPARADOR
            log.info("=== 📥 RESPUESTA DEL API COMPARADOR ===");
            log.info("🎯 Lead ID: {}", message.getLeadId());
            log.info("📦 Tamaño del body: {} campos", comparisonResult.size());
            log.info("🔑 Claves disponibles: {}", comparisonResult.keySet());

            // 📝 LOG DETALLADO DE CADA CAMPO DEL COMPARADOR
            for (Map.Entry<String, Object> entry : comparisonResult.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                String valueType = value != null ? value.getClass().getSimpleName() : "null";
                String valuePreview = value != null ?
                        (value.toString().length() > 100 ? value.toString().substring(0, 100) + "..." : value.toString())
                        : "null";
                log.info("   📝 {}: ({}) {}", key, valueType, valuePreview);
            }

            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para guardar comparación");
            }

            ClienteResidencial lead = leadOpt.get();

            // 🎯 EXTRAER PORCENTAJE DE COINCIDENCIA DEL API COMPARADOR
            Double porcentaje = extractPorcentajeFromComparadorResponse(comparisonResult);

            if (porcentaje != null) {
                // 🔧 USAR EL SERVICIO EXISTENTE PARA ACTUALIZAR LA NOTA
                java.math.BigDecimal notaBigDecimal = java.math.BigDecimal.valueOf(porcentaje);
                clienteResidencialService.actualizarNotaAgenteComparadorIA(message.getLeadId(), notaBigDecimal);
                log.info("💾 Nota del comparador guardada usando servicio existente: {}", porcentaje);
            } else {
                log.warn("⚠️ No se encontró porcentaje de coincidencia en la respuesta del comparador");
                log.warn("🔍 Campos disponibles: {}", comparisonResult.keySet());
            }
            log.info("✅ Resultado de comparación guardado para lead ID: {}", message.getLeadId());
            log.info("================================================");

        } catch (Exception e) {
            log.error("❌ Error al guardar resultado de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al guardar comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Extrae el porcentaje de coincidencia de la respuesta del API comparador
     */
    private Double extractPorcentajeFromComparadorResponse(Map<String, Object> comparisonResult) {
        try {
            // 🎯 MÉTODO 1: Buscar directamente "porcentaje_coincidencia"
            Object porcentajeObj = comparisonResult.get("porcentaje_coincidencia");
            if (porcentajeObj != null) {
                Double porcentaje = convertToDouble(porcentajeObj, "porcentaje_coincidencia");
                if (porcentaje != null) {
                    log.info("✅ Porcentaje encontrado en campo 'porcentaje_coincidencia': {}", porcentaje);
                    return porcentaje;
                }
            }

            // 🎯 MÉTODO 2: Buscar en otros campos posibles
            String[] possibleKeys = {"porcentaje", "coincidencia", "score", "percentage", "nota"};
            for (String key : possibleKeys) {
                Object obj = comparisonResult.get(key);
                if (obj != null) {
                    Double porcentaje = convertToDouble(obj, key);
                    if (porcentaje != null) {
                        log.info("✅ Porcentaje encontrado en campo '{}': {}", key, porcentaje);
                        return porcentaje;
                    }
                }
            }

            // 🎯 MÉTODO 3: Calcular porcentaje basado en campos booleanos (como en tu ejemplo)
            Double calculatedPercentage = calculatePercentageFromBooleanFields(comparisonResult);
            if (calculatedPercentage != null) {
                log.info("✅ Porcentaje calculado basado en campos booleanos: {}", calculatedPercentage);
                return calculatedPercentage;
            }

            log.warn("⚠️ No se encontró porcentaje de coincidencia en la respuesta del comparador");
            log.warn("🔍 Campos disponibles: {}", comparisonResult.keySet());
            return null;

        } catch (Exception e) {
            log.error("❌ Error al extraer porcentaje del comparador: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Convierte un objeto a Double de manera segura
     */
    private Double convertToDouble(Object obj, String fieldName) {
        try {
            if (obj instanceof Number) {
                return ((Number) obj).doubleValue();
            } else if (obj instanceof String) {
                String str = (String) obj;
                if (!str.trim().isEmpty()) {
                    return Double.parseDouble(str);
                }
            }
            return null;
        } catch (NumberFormatException e) {
            log.warn("⚠️ No se pudo convertir '{}' a número en campo '{}': {}", obj, fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * Calcula porcentaje basado en campos booleanos (para el formato del ejemplo)
     */
    private Double calculatePercentageFromBooleanFields(Map<String, Object> response) {
        try {
            int totalFields = 0;
            int trueFields = 0;

            // Contar campos booleanos que empiecen con "data."
            for (Map.Entry<String, Object> entry : response.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // Solo contar campos que empiecen con "data." y sean booleanos
                if (key.startsWith("data.") && value instanceof Boolean) {
                    totalFields++;
                    if ((Boolean) value) {
                        trueFields++;
                    }
                }
            }

            if (totalFields > 0) {
                double percentage = (trueFields * 100.0) / totalFields;
                log.info("📊 Cálculo de porcentaje: {}/{} campos verdaderos = {:.1f}%",
                        trueFields, totalFields, percentage);
                return percentage;
            }

            return null;

        } catch (Exception e) {
            log.warn("⚠️ Error al calcular porcentaje desde campos booleanos: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Envía mensaje a la cola de comparación
     */
    private void sendToComparisonQueue(TranscriptionQueueMessage message, Map<String, Object> transcriptionResult) {
        try {
            // Resetear intentos para la comparación
            message.setIntentos(0);
            message.setEstadoProcesamiento("PENDING");

            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.COMPARISON_EXCHANGE,
                    RabbitMQConfig.COMPARISON_ROUTING_KEY,
                    message
            );

            log.debug("Mensaje enviado a cola de comparación para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al enviar mensaje a cola de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al enviar a cola de comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Ejecuta automáticamente el comparador después de la transcripción
     */
    private void executeComparison(TranscriptionQueueMessage message, Map<String, Object> transcriptionResult) {
        try {
            log.info("Ejecutando comparación automática para lead ID: {}", message.getLeadId());

            // Llamar directamente a la API de comparación
            Map<String, Object> comparisonResult = callComparisonAPI(message);

            // Guardar resultado de comparación
            saveComparisonResult(message, comparisonResult);

            log.info("Comparación automática completada para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error en comparación automática para lead ID: {}", message.getLeadId(), e);
            // No lanzar excepción para no afectar el flujo principal de transcripción
        }
    }
}
