package com.midas.crm.service.serviceImpl;

import com.midas.crm.service.AudioConversionService;
import com.midas.crm.service.GoogleDriveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * Implementación del servicio de conversión de audio
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AudioConversionServiceImpl implements AudioConversionService {

    private final GoogleDriveService googleDriveService;

    @Value("${audio.conversion.temp.dir:${java.io.tmpdir}/audio-conversion}")
    private String tempDirectory;

    @Value("${audio.conversion.ffmpeg.path:ffmpeg}")
    private String ffmpegPath;

    @Value("${audio.conversion.upload.to.drive:true}")
    private boolean uploadToDrive;

    @Value("${audio.conversion.drive.folder.name:CONVERTED_MP3}")
    private String conversionFolderName;

    @Override
    public String convertGsmToMp3(String gsmFileUrl, String outputFileName) throws IOException {
        log.info("Iniciando conversión de GSM a MP3 para archivo: {}", outputFileName);

        // Verificar que FFmpeg esté disponible
        if (!isFFmpegAvailable()) {
            throw new IOException("FFmpeg no está disponible en el sistema");
        }

        // Crear directorio temporal si no existe
        Path tempDir = Paths.get(tempDirectory);
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        String uniqueId = UUID.randomUUID().toString();
        String gsmFilePath = tempDirectory + "/" + uniqueId + "_input.gsm";
        String mp3FilePath = tempDirectory + "/" + uniqueId + "_" + outputFileName + ".mp3";

        try {
            // 1. Descargar archivo GSM
            log.info("Descargando archivo GSM desde: {}", gsmFileUrl);
            downloadFile(gsmFileUrl, gsmFilePath);

            // 2. Convertir GSM a MP3 usando JAVE2
            log.info("Convirtiendo GSM a MP3: {} -> {}", gsmFilePath, mp3FilePath);
            convertWithJave2(gsmFilePath, mp3FilePath);

            // 3. Verificar que el archivo MP3 se creó correctamente
            Path mp3Path = Paths.get(mp3FilePath);
            if (!Files.exists(mp3Path) || Files.size(mp3Path) == 0) {
                throw new IOException("La conversión falló: archivo MP3 no generado o vacío");
            }

            // 4. Subir archivo MP3 convertido
            String mp3Url;
            if (uploadToDrive) {
                log.info("Subiendo archivo MP3 convertido a Google Drive");
                mp3Url = uploadMp3ToDrive(mp3FilePath, outputFileName + ".mp3");
            } else {
                // Alternativa: servir desde directorio temporal (solo para desarrollo)
                mp3Url = "file://" + mp3FilePath;
            }

            log.info("Conversión completada exitosamente. URL del MP3: {}", mp3Url);
            return mp3Url;

        } finally {
            // Limpiar archivos temporales
            cleanupTempFile(gsmFilePath);
            if (uploadToDrive) {
                cleanupTempFile(mp3FilePath);
            }
        }
    }

    @Override
    public boolean isFFmpegAvailable() {
        try {
            // Con JAVE2, FFmpeg está embebido, así que siempre está disponible
            Encoder encoder = new Encoder();
            log.info("JAVE2 (FFmpeg embebido) está disponible");
            return true;
        } catch (Exception e) {
            log.warn("JAVE2 no está disponible: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public void downloadFile(String fileUrl, String localPath) throws IOException {
        try {
            log.debug("Descargando archivo desde {} a {}", fileUrl, localPath);
            
            URL url = new URL(fileUrl);
            try (InputStream inputStream = url.openStream()) {
                Path targetPath = Paths.get(localPath);
                Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
            }
            
            log.debug("Archivo descargado exitosamente: {}", localPath);
        } catch (Exception e) {
            throw new IOException("Error al descargar archivo desde " + fileUrl, e);
        }
    }

    @Override
    public void cleanupTempFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.debug("Archivo temporal eliminado: {}", filePath);
            }
        } catch (Exception e) {
            log.warn("No se pudo eliminar archivo temporal {}: {}", filePath, e.getMessage());
        }
    }

    /**
     * Convierte archivo GSM a MP3 usando JAVE2
     */
    private void convertWithJave2(String inputPath, String outputPath) throws IOException {
        try {
            log.info("Iniciando conversión GSM a MP3 con JAVE2: {} -> {}", inputPath, outputPath);

            // Crear objetos de entrada y salida
            File source = new File(inputPath);
            File target = new File(outputPath);

            // Configurar atributos de audio para MP3
            AudioAttributes audio = new AudioAttributes();
            audio.setCodec("libmp3lame");
            audio.setBitRate(128000); // 128 kbps
            audio.setChannels(1);     // Mono (típico para grabaciones telefónicas)
            audio.setSamplingRate(44100); // 44.1 kHz

            // Configurar atributos de codificación
            EncodingAttributes attrs = new EncodingAttributes();
            attrs.setOutputFormat("mp3");
            attrs.setAudioAttributes(audio);

            // Realizar la conversión
            Encoder encoder = new Encoder();
            MultimediaObject source_obj = new MultimediaObject(source);
            encoder.encode(source_obj, target, attrs);

            log.info("Conversión JAVE2 completada exitosamente");

        } catch (EncoderException e) {
            throw new IOException("Error durante conversión con JAVE2: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new IOException("Error inesperado durante conversión", e);
        }
    }

    /**
     * Sube archivo MP3 convertido a Google Drive
     */
    private String uploadMp3ToDrive(String mp3FilePath, String fileName) throws IOException {
        try {
            log.info("Subiendo archivo MP3 convertido a Google Drive: {}", fileName);

            // 1. Buscar o crear carpeta para archivos convertidos
            String folderId = findOrCreateConversionFolder();

            // 2. Leer contenido del archivo MP3
            Path mp3Path = Paths.get(mp3FilePath);
            byte[] fileContent = Files.readAllBytes(mp3Path);

            // 3. Subir archivo a la carpeta específica en Google Drive
            String fileId = googleDriveService.uploadFile(fileContent, fileName, "audio/mpeg", folderId);

            // 4. Obtener URL de descarga directa
            String downloadUrl = googleDriveService.getDirectDownloadUrl(fileId);

            log.info("Archivo MP3 subido exitosamente a Google Drive. FileId: {}, URL: {}", fileId, downloadUrl);
            return downloadUrl;

        } catch (Exception e) {
            throw new IOException("Error al subir archivo MP3 a Google Drive", e);
        }
    }

    /**
     * Busca o crea la carpeta para archivos convertidos en Google Drive
     */
    private String findOrCreateConversionFolder() throws IOException {
        try {
            log.debug("Buscando carpeta de conversión: {}", conversionFolderName);

            // Primero intentar encontrar la carpeta existente
            String existingFolderId = googleDriveService.findFolderByName(conversionFolderName);

            if (existingFolderId != null) {
                log.debug("Carpeta de conversión encontrada: {}", existingFolderId);
                return existingFolderId;
            }

            // Si no existe, crear nueva carpeta
            log.info("Creando nueva carpeta de conversión: {}", conversionFolderName);
            String newFolderId = googleDriveService.createFolder(conversionFolderName);

            log.info("Carpeta de conversión creada exitosamente: {} (ID: {})", conversionFolderName, newFolderId);
            return newFolderId;

        } catch (Exception e) {
            throw new IOException("Error al buscar/crear carpeta de conversión", e);
        }
    }


}
