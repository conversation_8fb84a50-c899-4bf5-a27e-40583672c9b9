<div class="w-full max-w-4xl min-h-[500px]">
  <!-- Header -->
  <div class="px-6 pt-6 pb-4 border-b border-gray-200 bg-gradient-to-br from-indigo-500 to-purple-600 text-white -mx-6 -mt-6 mb-6">
    <h2 mat-dialog-title class="m-0 flex items-center gap-3 text-2xl font-medium">
      <mat-icon class="text-[28px] w-7 h-7">record_voice_over</mat-icon>
      Transcripción de Audio con IA
    </h2>
    <p class="mt-2 mb-1 text-[0.95rem] opacity-90" *ngIf="data.file || uploadedFile">
      Archivo: <strong>{{ uploadedFile ? uploadedFile.name : data.file?.name }}</strong>
      <span class="ml-2 text-xs opacity-75">
        ({{ uploadedFile ? 'Subido' : 'Google Drive' }})
      </span>
    </p>
    <p class="mt-2 mb-1 text-[0.95rem] opacity-90" *ngIf="!data.file && !uploadedFile && allowFileUpload">
      <strong>Subir archivo de audio para transcribir</strong>
    </p>

    <!-- Información del cliente y lead -->
    <div class="mt-3 space-y-1">
      <p class="mb-1 text-sm opacity-90" *ngIf="data.cliente">
        <mat-icon class="text-sm w-4 h-4 mr-1 align-text-bottom">person</mat-icon>
        Cliente: <strong>{{ getClientName() }}</strong>
        <span *ngIf="data.numeroMovil" class="ml-2">
          <mat-icon class="text-sm w-4 h-4 mr-1 align-text-bottom">phone</mat-icon>
          {{ data.numeroMovil }}
        </span>
      </p>

      <div class="flex flex-wrap gap-4 text-xs opacity-85" *ngIf="data.leadId || data.nombreAsesor || data.fechaCreacion || data.numeroAgente">
        <span *ngIf="data.leadId" class="flex items-center">
          <mat-icon class="text-xs w-3 h-3 mr-1">badge</mat-icon>
          ID: {{ data.leadId }}
        </span>
        <span *ngIf="data.nombreAsesor" class="flex items-center">
          <mat-icon class="text-xs w-3 h-3 mr-1">support_agent</mat-icon>
          Asesor: {{ data.nombreAsesor }}
        </span>
        <span *ngIf="data.fechaCreacion" class="flex items-center">
          <mat-icon class="text-xs w-3 h-3 mr-1">schedule</mat-icon>
          Creado: {{ formatFechaCreacion(data.fechaCreacion) }}
        </span>
        <span *ngIf="data.numeroAgente" class="flex items-center">
          <mat-icon class="text-xs w-3 h-3 mr-1">contact_support</mat-icon>
          Agente: {{ data.numeroAgente }}
        </span>
      </div>
    </div>
  </div>

  <!-- Estado de verificación -->
  <div *ngIf="isVerifying" class="px-6 py-4 bg-blue-50 border-l-4 border-blue-400 mx-6 mb-4 rounded-r-lg">
    <div class="flex items-center gap-3">
      <mat-spinner diameter="20" strokeWidth="3"></mat-spinner>
      <span class="text-sm text-blue-800">{{ statusMessage }}</span>
    </div>
  </div>

  <!-- Advertencia de transcripción existente -->
  <div *ngIf="showExistingTranscriptionWarning && verificationResult?.tieneTranscripcion"
       class="px-6 py-4 bg-orange-50 border-l-4 border-orange-400 mx-6 mb-4 rounded-r-lg">
    <div class="flex items-start gap-3">
      <mat-icon class="text-orange-600 text-6 w-6 h-6 mt-0.5">warning</mat-icon>
      <div class="flex-1">
        <h4 class="m-0 mb-2 text-orange-800 font-medium">⚠️ Cliente ya tiene transcripción</h4>
        <p class="text-sm text-orange-700 mb-3 leading-relaxed">
          {{ verificationResult?.mensaje }}
        </p>
        <div class="text-xs text-orange-600 space-y-1">
          <div *ngIf="verificationResult?.nombreCliente">
            <strong>Cliente:</strong> {{ verificationResult?.nombreCliente }}
          </div>
          <div *ngIf="verificationResult?.tieneTextoTranscripcion">
            ✓ Tiene texto de transcripción guardado
          </div>
          <div *ngIf="verificationResult?.tieneUrlDriveTranscripcion">
            ✓ Tiene archivo en Google Drive
          </div>
        </div>
        <div class="mt-3 flex gap-2">
          <button *ngIf="verificationResult?.urlDriveTranscripcion"
                  mat-stroked-button
                  color="primary"
                  (click)="openExistingTranscription()"
                  class="text-xs">
            <mat-icon class="text-4 w-4 h-4">open_in_new</mat-icon>
            Ver transcripción existente
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Contenido principal -->
  <div mat-dialog-content class="p-0 max-h-[70vh] overflow-y-auto">

    <!-- Paso 1: Configuración -->
    <div *ngIf="currentStep === 'config'" class="p-6">

      <!-- Zona de subida de archivos (solo si allowFileUpload es true y no hay archivo) -->
      <div *ngIf="allowFileUpload && !data.file && !uploadedFile" class="mb-8">
        <div class="text-center mb-6">
          <mat-icon class="text-[64px] w-16 h-16 text-indigo-500 mb-4">audiotrack</mat-icon>
          <h3 class="m-0 mb-2 text-2xl text-gray-800 font-medium">Transcripción de Audio con IA</h3>
          <p class="text-gray-600">Sube tu archivo de audio para obtener una transcripción automática</p>
        </div>

        <div class="file-upload-zone border-2 border-dashed border-indigo-300 rounded-xl p-12 text-center transition-all duration-300 cursor-pointer hover:border-indigo-500 hover:bg-indigo-50 hover:shadow-lg"
             [class.border-indigo-600]="isDragOver"
             [class.bg-indigo-100]="isDragOver"
             [class.shadow-xl]="isDragOver"
             (dragover)="onDragOver($event)"
             (dragleave)="onDragLeave($event)"
             (drop)="onDrop($event)"
             (click)="fileInput.click()">

          <input #fileInput
                 type="file"
                 accept="audio/*,.mp3,.wav,.m4a,.ogg,.flac"
                 (change)="onFileSelected($event)"
                 style="display: none;">

          <div class="upload-content">
            <div class="mb-6">
              <mat-icon class="text-[72px] w-18 h-18 text-indigo-400 mb-4 transition-transform duration-300 hover:scale-110">cloud_upload</mat-icon>
            </div>
            <h4 class="m-0 mb-3 text-xl text-gray-700 font-semibold">Selecciona tu archivo de audio</h4>
            <p class="text-gray-600 mb-6 text-lg">Arrastra y suelta aquí o haz clic para explorar</p>

            <!-- Botones de selección -->
            <div class="flex flex-col sm:flex-row gap-3 justify-center mb-6" (click)="$event.stopPropagation()">
              <button type="button"
                      mat-raised-button
                      color="primary"
                      (click)="openFileSelector($event)"
                      class="px-8 py-3 text-lg">
                <mat-icon class="mr-2">folder_open</mat-icon>
                Seleccionar Archivo
              </button>

              <button type="button"
                      mat-raised-button
                      color="accent"
                      (click)="openGoogleDriveExplorer($event)"
                      [disabled]="isProcessing || isVerifying"
                      class="px-8 py-3 text-lg"
                      matTooltip="Seleccionar archivo de audio desde Google Drive">
                <mat-icon class="mr-2">cloud</mat-icon>
                Google Drive
              </button>
            </div>

            <!-- Información de formatos -->
            <div class="bg-gray-50 rounded-lg p-4 mx-auto max-w-md">
              <div class="text-sm text-gray-600 space-y-2">
                <div class="flex items-center justify-center gap-2 mb-3">
                  <mat-icon class="text-green-600 text-sm">check_circle</mat-icon>
                  <span class="font-medium">Formatos Soportados</span>
                </div>
                <div class="flex flex-wrap justify-center gap-2 mb-3">
                  <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">MP3</span>
                  <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">WAV</span>
                  <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">M4A</span>
                  <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">OGG</span>
                  <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">FLAC</span>
                </div>
                <p class="text-xs text-gray-500">Tamaño máximo: 100MB</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Información del archivo seleccionado (archivo local) -->
      <div *ngIf="uploadedFile" class="mb-8">
        <h3 class="m-0 mb-4 text-gray-800 font-medium">Archivo Seleccionado</h3>

        <div class="flex items-center gap-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <mat-icon class="text-green-600 text-[32px] w-8 h-8">audiotrack</mat-icon>
          <div class="flex-1">
            <h4 class="m-0 mb-1 text-gray-800 font-medium">{{ uploadedFile.name }}</h4>
            <p class="text-sm text-gray-600 mb-0">
              {{ formatFileSize(uploadedFile.size) }} • {{ uploadedFile.type }}
            </p>
          </div>
          <button type="button"
                  mat-icon-button
                  color="warn"
                  (click)="removeUploadedFile()"
                  matTooltip="Eliminar archivo">
            <mat-icon>delete</mat-icon>
          </button>
        </div>
      </div>

      <!-- Información del archivo seleccionado (Google Drive) -->
      <div *ngIf="data.file && !uploadedFile" class="mb-8">
        <h3 class="m-0 mb-4 text-gray-800 font-medium">Archivo Seleccionado desde Google Drive</h3>

        <div class="flex items-center gap-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <mat-icon class="text-blue-600 text-[32px] w-8 h-8">cloud</mat-icon>
          <div class="flex-1">
            <h4 class="m-0 mb-1 text-gray-800 font-medium">{{ data.file.name }}</h4>
            <p class="text-sm text-gray-600 mb-0">
              <span *ngIf="data.file.size">{{ formatFileSize(data.file.size) }} • </span>
              <span *ngIf="data.file.mimeType">{{ data.file.mimeType }}</span>
              <span *ngIf="!data.file.mimeType && data.file.name">{{ getFileExtension(data.file.name) }}</span>
            </p>
          </div>
          <button type="button"
                  mat-icon-button
                  color="warn"
                  (click)="removeGoogleDriveFile()"
                  matTooltip="Eliminar archivo">
            <mat-icon>delete</mat-icon>
          </button>
        </div>
      </div>

      <h3 class="m-0 mb-6 text-gray-800 font-medium">Configuración de Transcripción</h3>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Modelo Whisper -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Modelo Whisper</mat-label>
          <mat-select [(value)]="transcriptionConfig.whisper_model" panelClass="whisper-model-panel">
            <mat-option *ngFor="let model of whisperModels" [value]="model.value">
              <div class="flex flex-col py-1">
                <span class="font-medium text-sm text-gray-900 mb-1">{{ model.label }}</span>
                <div class="flex gap-4 text-xs text-gray-600">
                  <span>Velocidad: {{ model.speed }}</span>
                  <span>Precisión: {{ model.accuracy }}</span>
                </div>
              </div>
            </mat-option>
          </mat-select>
          <mat-hint>Selecciona el balance entre velocidad y precisión</mat-hint>
        </mat-form-field>

        <!-- Dispositivo -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Dispositivo de Procesamiento</mat-label>
          <mat-select [(value)]="transcriptionConfig.device">
            <mat-option *ngFor="let device of devices" [value]="device.value">
              {{ device.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Idioma -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Idioma de Destino</mat-label>
          <input matInput name="target_language" [(ngModel)]="transcriptionConfig.target_language" placeholder="es">
          <mat-hint>Código de idioma (es, en, fr, etc.)</mat-hint>
        </mat-form-field>

        <!-- Tipo de llamada -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Tipo de Llamada</mat-label>
          <mat-select [(value)]="transcriptionConfig.call_type">
            <mat-option *ngFor="let type of callTypes" [value]="type.value">
              {{ type.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- ID del agente -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>ID del Agente</mat-label>
          <input matInput name="agent_id" [(ngModel)]="transcriptionConfig.agent_id" placeholder="Opcional">
        </mat-form-field>

        <!-- Fecha y hora -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Fecha y Hora de la Llamada</mat-label>
          <input matInput name="call_datetime" type="datetime-local" [(ngModel)]="transcriptionConfig.call_datetime">
        </mat-form-field>
      </div>
    </div>

    <!-- Paso 2: Procesando -->
    <div *ngIf="currentStep === 'transcribing'" class="py-12 px-6 text-center">
      <div class="max-w-md mx-auto">
        <mat-icon class="text-[64px] w-16 h-16 text-indigo-500 mb-4 animate-pulse">psychology</mat-icon>
        <h3 class="m-0 mb-6 text-gray-800 font-medium">{{ statusMessage }}</h3>

        <mat-progress-bar mode="indeterminate" class="h-2 rounded-md mb-4"></mat-progress-bar>
        <p class="text-sm text-gray-600 mb-2 leading-relaxed">
          El servidor está procesando el audio con inteligencia artificial.<br>
          <strong>Este proceso puede tomar varios minutos dependiendo del tamaño del archivo.</strong><br>
          Por favor, mantén esta ventana abierta.
        </p>

        <div class="flex justify-between mt-8 space-x-4 md:space-x-0 md:flex-row flex-col">
          <div class="flex flex-col items-center gap-2 opacity-100 text-green-500 transition-opacity duration-300">
            <mat-icon class="text-8 w-8 h-8">upload_file</mat-icon>
            <span class="text-xs text-center">Archivo enviado</span>
          </div>
          <div class="flex flex-col items-center gap-2 opacity-100 text-indigo-500 transition-opacity duration-300">
            <mat-icon class="text-8 w-8 h-8 animate-bounce">record_voice_over</mat-icon>
            <span class="text-xs text-center">Transcribiendo audio</span>
          </div>
          <div class="flex flex-col items-center gap-2 opacity-40 transition-opacity duration-300">
            <mat-icon class="text-8 w-8 h-8">psychology</mat-icon>
            <span class="text-xs text-center">Analizando emociones</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Paso 3: Completado -->
    <div *ngIf="currentStep === 'completed' && transcriptionResult" class="p-6">
      <div class="text-center mb-8">
        <mat-icon class="text-[48px] w-12 h-12 text-green-500 mb-4">check_circle</mat-icon>
        <h3 class="m-0 text-gray-800 font-medium">¡Transcripción Completada!</h3>
      </div>

      <!-- Información del procesamiento -->
      <div class="mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <mat-icon class="text-gray-600 text-5 w-5 h-5">schedule</mat-icon>
            <span class="text-sm text-gray-800">Duración: {{ formatDuration(transcriptionResult.call_duration || 0) }}</span>
          </div>
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <mat-icon class="text-gray-600 text-5 w-5 h-5">memory</mat-icon>
            <span class="text-sm text-gray-800">Modelo: {{ transcriptionResult.model_used || 'N/A' }}</span>
          </div>
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <mat-icon class="text-gray-600 text-5 w-5 h-5">language</mat-icon>
            <span class="text-sm text-gray-800">Idioma: {{ transcriptionResult.language_detected || 'N/A' }}</span>
          </div>
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <mat-icon class="text-gray-600 text-5 w-5 h-5">timer</mat-icon>
            <span class="text-sm text-gray-800">Tiempo de procesamiento: {{ formatProcessingTime(transcriptionResult.processing_time || 0) }}</span>
          </div>
        </div>
      </div>

      <!-- Información adicional del análisis -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="m-0 text-gray-800 font-medium">Información del Análisis</h4>
          <button mat-button (click)="toggleAdvancedInfo()" class="text-blue-600">
            <mat-icon>{{ showAdvancedInfo ? 'expand_less' : 'expand_more' }}</mat-icon>
            {{ showAdvancedInfo ? 'Menos detalles' : 'Más detalles' }}
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Estado de ánimo -->
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg" *ngIf="transcriptionResult.mood">
            <mat-icon class="text-gray-600 text-5 w-5 h-5" [style.color]="getSentimentColor(transcriptionResult.mood)">
              {{ getSentimentIcon(transcriptionResult.mood) }}
            </mat-icon>
            <span class="text-sm text-gray-800">Estado: {{ translateSentiment(transcriptionResult.mood) }}</span>
          </div>

          <!-- Confianza del estado de ánimo -->
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg" *ngIf="transcriptionResult.mood_confidence">
            <mat-icon class="text-gray-600 text-5 w-5 h-5">psychology</mat-icon>
            <span class="text-sm text-gray-800">Confianza: {{ (transcriptionResult.mood_confidence * 100).toFixed(1) }}%</span>
          </div>

          <!-- Número de palabras -->
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg" *ngIf="transcriptionResult.word_count">
            <mat-icon class="text-gray-600 text-5 w-5 h-5">format_list_numbered</mat-icon>
            <span class="text-sm text-gray-800">Palabras: {{ transcriptionResult.word_count }}</span>
          </div>

          <!-- Calidad del audio -->
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg" *ngIf="transcriptionResult.audio_quality_score !== undefined">
            <mat-icon class="text-gray-600 text-5 w-5 h-5" [style.color]="getAudioQualityColor(transcriptionResult.audio_quality_score)">
              volume_up
            </mat-icon>
            <span class="text-sm text-gray-800">Calidad: {{ getAudioQualityDescription(transcriptionResult.audio_quality_score) }}</span>
          </div>
        </div>

        <!-- Información avanzada -->
        <div *ngIf="showAdvancedInfo" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Tamaño del archivo -->
          <div class="flex items-center gap-2 p-3 bg-blue-50 rounded-lg" *ngIf="transcriptionResult.file_size_mb">
            <mat-icon class="text-blue-600 text-5 w-5 h-5">storage</mat-icon>
            <span class="text-sm text-gray-800">Tamaño: {{ formatFileSize(transcriptionResult.file_size_mb * 1024 * 1024) }}</span>
          </div>

          <!-- Dispositivo usado -->
          <div class="flex items-center gap-2 p-3 bg-blue-50 rounded-lg" *ngIf="transcriptionResult.device_used">
            <mat-icon class="text-blue-600 text-5 w-5 h-5">{{ transcriptionResult.device_used === 'gpu' ? 'memory' : 'computer' }}</mat-icon>
            <span class="text-sm text-gray-800">Dispositivo: {{ transcriptionResult.device_used.toUpperCase() }}</span>
          </div>

          <!-- Velocidad de procesamiento -->
          <div class="flex items-center gap-2 p-3 bg-blue-50 rounded-lg" *ngIf="transcriptionResult.performance_metrics?.processing_speed">
            <mat-icon class="text-blue-600 text-5 w-5 h-5">speed</mat-icon>
            <span class="text-sm text-gray-800">Velocidad: {{ transcriptionResult.performance_metrics?.processing_speed }}</span>
          </div>

          <!-- Palabras por segundo -->
          <div class="flex items-center gap-2 p-3 bg-blue-50 rounded-lg" *ngIf="transcriptionResult.performance_metrics?.words_per_second">
            <mat-icon class="text-blue-600 text-5 w-5 h-5">trending_up</mat-icon>
            <span class="text-sm text-gray-800">Palabras/seg: {{ transcriptionResult.performance_metrics?.words_per_second }}</span>
          </div>
        </div>

        <!-- Advertencias -->
        <div *ngIf="transcriptionResult.warnings && transcriptionResult.warnings.length > 0" class="mt-4">
          <h5 class="text-sm font-medium text-orange-700 mb-2">Advertencias:</h5>
          <div class="space-y-2">
            <div *ngFor="let warning of transcriptionResult.warnings"
                 class="flex items-start gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <mat-icon class="text-orange-600 text-5 w-5 h-5 mt-0.5">warning</mat-icon>
              <div class="flex-1">
                <p class="text-sm text-orange-800 m-0">{{ warning.message }}</p>
                <p *ngIf="warning.recommendation" class="text-xs text-orange-600 mt-1 m-0">{{ warning.recommendation }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Transcripción -->
      <div>
        <div class="flex justify-between items-center mb-3">
          <h4 class="m-0 text-gray-800 font-medium">Transcripción</h4>
          <div class="flex gap-2">
            <button mat-icon-button (click)="copyTranscription()" matTooltip="Copiar transcripción">
              <mat-icon>content_copy</mat-icon>
            </button>
            <button mat-raised-button
                    color="accent"
                    (click)="compararLead()"
                    matTooltip="Comparar con datos del lead"
                    class="text-sm">
              <mat-icon class="text-4 w-4 h-4">compare_arrows</mat-icon>
              Comparar Lead
            </button>
          </div>
        </div>
        <div class="p-5 bg-gray-50 rounded-lg border border-gray-200 font-sans leading-relaxed text-gray-800 whitespace-pre-wrap max-h-[200px] overflow-y-auto">
          {{ transcriptionResult.transcription }}
        </div>
      </div>
    </div>

    <!-- Paso 4: Error -->
    <div *ngIf="currentStep === 'error'" class="py-12 px-6 text-center">
      <div class="max-w-md mx-auto">
        <mat-icon class="text-[64px] w-16 h-16 text-red-500 mb-4">error</mat-icon>
        <h3 class="m-0 mb-4 text-gray-800 font-medium">Error en la Transcripción</h3>
        <p class="text-gray-600 mb-6 leading-relaxed">{{ statusMessage }}</p>
        <button mat-raised-button color="primary" (click)="resetTranscription()">
          <mat-icon>refresh</mat-icon>
          Intentar de Nuevo
        </button>
      </div>
    </div>
  </div>

  <!-- Footer con acciones -->
  <div mat-dialog-actions class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end gap-3">
    <button mat-button (click)="close()">
      {{ currentStep === 'completed' ? 'Cerrar' : 'Cancelar' }}
    </button>

    <button *ngIf="currentStep === 'config'"
            type="button"
            mat-raised-button
            [color]="verificationResult?.tieneTranscripcion ? 'warn' : 'primary'"
            (click)="startTranscription()"
            [disabled]="isProcessing || isVerifying || (!data.file && !uploadedFile)">
      <mat-icon>{{ verificationResult?.tieneTranscripcion ? 'warning' : 'play_arrow' }}</mat-icon>
      {{ verificationResult?.tieneTranscripcion ? 'Sobrescribir Transcripción' : 'Iniciar Transcripción' }}
    </button>

    <button *ngIf="currentStep === 'completed'"
            mat-raised-button
            color="primary"
            (click)="compararLead()">
      <mat-icon>compare_arrows</mat-icon>
      Comparar Lead
    </button>

    <button *ngIf="currentStep === 'completed'"
            mat-raised-button
            color="accent"
            (click)="resetTranscription()">
      <mat-icon>refresh</mat-icon>
      Nueva Transcripción
    </button>
  </div>
</div>
