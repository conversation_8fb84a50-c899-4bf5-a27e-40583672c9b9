import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface TranscriptionQueueMessage {
  leadId: number;
  numeroMovil: string;
  numeroAgente: string;
  numeroAgenteNormalizado: string;
  fechaCreacion: string;
  nombreAsesor: string;
  fechaEnvio: string;
  intentos: number;
  maxIntentos: number;
  queueName: string;
  routingKey: string;
}

export interface QueueStatistics {
  totalLeads: number;
  leadsWithTranscription: number;
  leadsWithoutTranscription: number;
  leadsWithNotes: number;
  timestamp: string;
  agentStats: { [key: string]: any };
}

export interface ProcessingResult {
  totalFound: number;
  sentToQueue: number;
  errors: number;
  errorMessages: string[];
  timestamp: string;
  numeroAgente?: string;
  batchSize: number;
}

export interface GenericResponse<T> {
  rpta: number;
  msg: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class TranscriptionQueueService {
  private readonly baseUrl = `${environment.url}api/transcription-queue`;

  constructor(private http: HttpClient) {}

  /**
   * Procesa leads pendientes de transcripción
   */
  processPendingLeads(batchSize: number = 3, numeroAgente?: string): Observable<GenericResponse<ProcessingResult>> {
    let params = new HttpParams().set('batchSize', batchSize.toString());

    if (numeroAgente) {
      params = params.set('numeroAgente', numeroAgente);
    }

    // 📋 LOGS DETALLADOS DE LO QUE SE ENVÍA AL BACKEND
    console.log('=== 📋 DATOS ENVIADOS AL API DE PROCESAMIENTO ===');
    console.log('🎯 URL:', `${this.baseUrl}/process-pending-leads`);
    console.log('📦 Batch Size:', batchSize);
    console.log('👤 Número Agente:', numeroAgente || 'No especificado');
    console.log('🔗 Parámetros completos:', params.toString());
    console.log('⏰ Timestamp:', new Date().toISOString());
    console.log('================================================');

    return this.http.post<GenericResponse<ProcessingResult>>(`${this.baseUrl}/process-pending-leads`, null, { params })
      .pipe(
        tap(response => {
          // 📥 LOG DE LA RESPUESTA RECIBIDA
          console.log('=== 📥 RESPUESTA DEL API DE PROCESAMIENTO ===');
          console.log('📊 Status:', response.rpta);
          console.log('💬 Mensaje:', response.msg);
          console.log('📄 Data completa:', response.data);
          console.log('⏰ Timestamp respuesta:', new Date().toISOString());
          console.log('================================================');
        }),
        catchError((error: any) => {
          // ❌ LOG DE ERRORES
          console.error('=== ❌ ERROR EN API DE PROCESAMIENTO ===');
          console.error('🚨 Error completo:', error);
          console.error('📊 Status Code:', error.status);
          console.error('💬 Mensaje:', error.message);
          console.error('📄 Error body:', error.error);
          console.error('⏰ Timestamp error:', new Date().toISOString());
          console.error('================================================');
          return throwError(() => error);
        })
      );
  }

  /**
   * Obtiene estadísticas de las colas
   */
  getQueueStatistics(): Observable<GenericResponse<QueueStatistics>> {
    return this.http.get<GenericResponse<QueueStatistics>>(`${this.baseUrl}/queue-stats`);
  }

  /**
   * Obtiene leads pendientes de transcripción
   */
  getPendingLeads(limit: number = 50, numeroAgente?: string): Observable<GenericResponse<TranscriptionQueueMessage[]>> {
    let params = new HttpParams().set('limit', limit.toString());
    
    if (numeroAgente) {
      params = params.set('numeroAgente', numeroAgente);
    }

    return this.http.get<GenericResponse<TranscriptionQueueMessage[]>>(`${this.baseUrl}/pending-leads`, { params });
  }

  /**
   * Reintenta mensajes fallidos
   */
  retryFailedMessages(maxRetries: number = 10): Observable<GenericResponse<any>> {
    const params = new HttpParams().set('maxRetries', maxRetries.toString());
    return this.http.post<GenericResponse<any>>(`${this.baseUrl}/retry-failed`, null, { params });
  }

  /**
   * Envía un lead específico a la cola
   */
  sendLeadToQueue(leadId: number): Observable<GenericResponse<boolean>> {
    return this.http.post<GenericResponse<boolean>>(`${this.baseUrl}/send-to-queue/${leadId}`, null);
  }

  /**
   * Pausa el procesamiento de colas
   */
  pauseProcessing(): Observable<GenericResponse<string>> {
    return this.http.post<GenericResponse<string>>(`${this.baseUrl}/pause`, null);
  }

  /**
   * Reanuda el procesamiento de colas
   */
  resumeProcessing(): Observable<GenericResponse<string>> {
    return this.http.post<GenericResponse<string>>(`${this.baseUrl}/resume`, null);
  }

  /**
   * Verifica si un lead es elegible para procesamiento
   */
  isLeadEligible(leadId: number): Observable<GenericResponse<boolean>> {
    return this.http.get<GenericResponse<boolean>>(`${this.baseUrl}/eligible/${leadId}`);
  }

  /**
   * Busca archivo MP3 para un agente específico
   */
  findMp3ForAgent(numeroAgente: string, folderId?: string): Observable<GenericResponse<any>> {
    let params = new HttpParams();
    if (folderId) {
      params = params.set('folderId', folderId);
    }

    return this.http.get<GenericResponse<any>>(`${environment.url}api/google-drive/mp3-url/${numeroAgente}`, { params });
  }

  /**
   * Busca archivo de audio por móvil y agente
   */
  findAudioByMovilAndAgent(numeroMovil: string, numeroAgente: string, folderId?: string): Observable<GenericResponse<any>> {
    let params = new HttpParams()
      .set('numeroMovil', numeroMovil)
      .set('numeroAgente', numeroAgente);
    
    if (folderId) {
      params = params.set('folderId', folderId);
    }

    return this.http.get<GenericResponse<any>>(`${environment.url}api/google-drive/audio-url`, { params });
  }
}
