package com.midas.crm.controller;

import com.midas.crm.entity.Leccion;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.repository.LeccionRepository;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Optional;

/**
 * Controlador que actúa como proxy para cargar subtítulos desde Firebase Storage
 * Soluciona el problema de CORS al acceder directamente a Firebase desde el frontend
 */
@RestController
@RequestMapping("/api/subtitulos-proxy")
@Slf4j
public class SubtitulosProxyController {

    private final LeccionRepository leccionRepository;
    private final RestTemplate restTemplate;

    public SubtitulosProxyController(LeccionRepository leccionRepository,
                                   @Qualifier("standardRestTemplate") RestTemplate restTemplate) {
        this.leccionRepository = leccionRepository;
        this.restTemplate = restTemplate;
    }

    /**
     * Obtiene los subtítulos de una lección por su ID
     * @param leccionId ID de la lección
     * @return ResponseEntity con el contenido de los subtítulos
     */
    @GetMapping("/leccion/{leccionId}")
    public ResponseEntity<byte[]> getSubtitulosByLeccionId(@PathVariable Long leccionId) {
        try {
            // Buscar la lección en la base de datos
            Optional<Leccion> leccionOpt = leccionRepository.findById(leccionId);
            if (leccionOpt.isEmpty()) {
                throw new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND);
            }

            Leccion leccion = leccionOpt.get();
            String subtitlesUrl = leccion.getSubtitlesUrl();

            // Verificar si la lección tiene URL de subtítulos
            if (subtitlesUrl == null || subtitlesUrl.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            // Descargar los subtítulos desde Firebase Storage
            byte[] subtitlesContent = downloadFromFirebase(subtitlesUrl);

            // Configurar los headers de la respuesta
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.valueOf("text/vtt"));
            headers.setContentDispositionFormData("attachment", "subtitles.vtt");

            // Devolver los subtítulos
            return new ResponseEntity<>(subtitlesContent, headers, HttpStatus.OK);
        } catch (MidasExceptions e) {
            log.error("Error al obtener los subtítulos de la lección {}: {}", leccionId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            log.error("Error al descargar los subtítulos de la lección {}: {}", leccionId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Obtiene los subtítulos directamente desde una URL de Firebase
     * @return ResponseEntity con el contenido de los subtítulos
     */
    @GetMapping("/url")
    public ResponseEntity<byte[]> getSubtitulosByUrl(@RequestParam String url) {
        try {
            // Verificar si la URL es válida
            if (url == null || url.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            // Descargar los subtítulos desde Firebase Storage
            byte[] subtitlesContent = downloadFromFirebase(url);

            // Configurar los headers de la respuesta
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.valueOf("text/vtt"));
            headers.setContentDispositionFormData("attachment", "subtitles.vtt");

            // Devolver los subtítulos
            return new ResponseEntity<>(subtitlesContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error al descargar los subtítulos desde la URL {}: {}", url, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Descarga el contenido de una URL de Firebase Storage
     * @param firebaseUrl URL de Firebase Storage
     * @return Contenido descargado como array de bytes
     * @throws IOException Si ocurre un error al descargar el contenido
     * @throws URISyntaxException Si la URL no es válida
     */
    private byte[] downloadFromFirebase(String firebaseUrl) throws IOException, URISyntaxException {
        // Asegurarse de que la URL incluye el parámetro alt=media para descargar el contenido
        if (!firebaseUrl.contains("alt=media")) {
            firebaseUrl = firebaseUrl.contains("?")
                    ? firebaseUrl + "&alt=media"
                    : firebaseUrl + "?alt=media";
        }

        log.info("Descargando subtítulos desde: {}", firebaseUrl);

        // Usar RestTemplate para descargar el contenido
        return restTemplate.getForObject(new URI(firebaseUrl), byte[].class);
    }
}
