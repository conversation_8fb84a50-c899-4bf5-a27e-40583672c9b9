<!-- Header del diálogo -->
<div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
  <div>
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
      {{ data.title || 'Procesamiento de Transcripciones' }}
    </h2>
    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
      <PERSON><PERSON> leads sin notas y procesar transcripciones automáticamente
    </p>
  </div>
  <button
    mat-icon-button
    (click)="close()"
    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
    <mat-icon>close</mat-icon>
  </button>
</div>

<!-- Contenido principal -->
<div mat-dialog-content class="p-6 max-h-[70vh] overflow-auto">
  
  <!-- Estadísticas -->
  <div *ngIf="statistics" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
      <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ statistics.totalLeads }}</div>
      <div class="text-sm text-blue-700 dark:text-blue-300">Total Leads</div>
    </div>
    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
      <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ statistics.leadsWithTranscription }}</div>
      <div class="text-sm text-green-700 dark:text-green-300">Con Transcripción</div>
    </div>
    <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
      <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ statistics.leadsWithoutTranscription }}</div>
      <div class="text-sm text-orange-700 dark:text-orange-300">Sin Transcripción</div>
    </div>
    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
      <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ statistics.leadsWithNotes }}</div>
      <div class="text-sm text-purple-700 dark:text-purple-300">Con Notas</div>
    </div>
  </div>

  <!-- Controles de filtrado y procesamiento -->
  <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
      <!-- Filtro por agente -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Filtrar por Agente (opcional)
        </label>
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Número de Agente</mat-label>
          <input matInput [formControl]="numeroAgenteControl" placeholder="Ej: 23, 007, agen026">
          <mat-icon matSuffix>person</mat-icon>
        </mat-form-field>
      </div>

      <!-- Tamaño del lote -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Leads por Lote
        </label>
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Batch Size</mat-label>
          <input matInput type="number" [formControl]="batchSizeControl" min="1" max="10">
          <mat-icon matSuffix>batch_prediction</mat-icon>
        </mat-form-field>
      </div>

      <!-- Botón de procesamiento -->
      <div>
        <button
          mat-raised-button
          color="primary"
          (click)="processSelectedLeads()"
          [disabled]="processing || loading"
          class="w-full h-14">
          <mat-icon class="mr-2">play_arrow</mat-icon>
          {{ processing ? 'Procesando...' : 'Procesar Leads' }}
        </button>
      </div>
    </div>

    <!-- Indicador de procesamiento -->
    <div *ngIf="processing" class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
      <div class="flex items-center mb-2">
        <mat-spinner diameter="20" class="mr-3"></mat-spinner>
        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ processingStatus.currentStep }}</span>
      </div>
      <div class="text-xs text-blue-600 dark:text-blue-400">{{ processingStatus.message }}</div>
      <mat-progress-bar mode="determinate" [value]="processingStatus.progress" class="mt-2"></mat-progress-bar>
    </div>
  </div>

  <!-- Resultado del último procesamiento -->
  <div *ngIf="lastProcessingResult" class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800 mb-6">
    <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Último Procesamiento</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
      <div>
        <span class="text-green-700 dark:text-green-300">Encontrados:</span>
        <span class="font-bold ml-1">{{ lastProcessingResult.totalFound }}</span>
      </div>
      <div>
        <span class="text-green-700 dark:text-green-300">Enviados:</span>
        <span class="font-bold ml-1">{{ lastProcessingResult.sentToQueue }}</span>
      </div>
      <div>
        <span class="text-green-700 dark:text-green-300">Errores:</span>
        <span class="font-bold ml-1">{{ lastProcessingResult.errors }}</span>
      </div>
      <div>
        <span class="text-green-700 dark:text-green-300">Lote:</span>
        <span class="font-bold ml-1">{{ lastProcessingResult.batchSize }}</span>
      </div>
    </div>
  </div>

  <!-- Lista de leads pendientes -->
  <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Leads Pendientes ({{ pendingLeads.length }})
        </h3>
        <button
          mat-button
          (click)="loadPendingLeads()"
          [disabled]="loading"
          class="text-blue-600 hover:text-blue-800">
          <mat-icon class="mr-1">refresh</mat-icon>
          Actualizar
        </button>
      </div>
    </div>

    <!-- Tabla de leads -->
    <div class="overflow-x-auto">
      <table mat-table [dataSource]="pendingLeads" class="w-full">
        
        <!-- Columna de selección -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef class="w-12">
            <mat-checkbox
              (change)="toggleAllSelection()"
              [checked]="isAllSelected()"
              [indeterminate]="selectedLeads.length > 0 && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let lead" class="w-12">
            <mat-checkbox
              (change)="toggleLeadSelection(lead)"
              [checked]="isLeadSelected(lead)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Columna Lead ID -->
        <ng-container matColumnDef="leadId">
          <th mat-header-cell *matHeaderCellDef class="text-left font-medium">ID</th>
          <td mat-cell *matCellDef="let lead" class="text-sm">{{ lead.leadId }}</td>
        </ng-container>

        <!-- Columna Número Móvil -->
        <ng-container matColumnDef="numeroMovil">
          <th mat-header-cell *matHeaderCellDef class="text-left font-medium">Móvil</th>
          <td mat-cell *matCellDef="let lead" class="text-sm font-mono">{{ lead.numeroMovil }}</td>
        </ng-container>

        <!-- Columna Agente -->
        <ng-container matColumnDef="numeroAgente">
          <th mat-header-cell *matHeaderCellDef class="text-left font-medium">Agente</th>
          <td mat-cell *matCellDef="let lead" class="text-sm">
            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">
              {{ lead.numeroAgenteNormalizado }}
            </span>
          </td>
        </ng-container>

        <!-- Columna Asesor -->
        <ng-container matColumnDef="nombreAsesor">
          <th mat-header-cell *matHeaderCellDef class="text-left font-medium">Asesor</th>
          <td mat-cell *matCellDef="let lead" class="text-sm">{{ lead.nombreAsesor }}</td>
        </ng-container>

        <!-- Columna Fecha -->
        <ng-container matColumnDef="fechaCreacion">
          <th mat-header-cell *matHeaderCellDef class="text-left font-medium">Fecha</th>
          <td mat-cell *matCellDef="let lead" class="text-sm">{{ lead.fechaCreacion | date:'short' }}</td>
        </ng-container>

        <!-- Columna Acciones -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="text-left font-medium">Acciones</th>
          <td mat-cell *matCellDef="let lead" class="text-sm">
            <div class="flex gap-2">
              <button
                mat-icon-button
                (click)="searchMp3ForLead(lead)"
                matTooltip="Buscar archivo MP3"
                class="text-blue-600 hover:text-blue-800">
                <mat-icon>search</mat-icon>
              </button>
              <button
                mat-icon-button
                (click)="sendLeadToQueue(lead)"
                matTooltip="Enviar a cola"
                class="text-green-600 hover:text-green-800">
                <mat-icon>send</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
            class="hover:bg-gray-50 dark:hover:bg-gray-800"></tr>
      </table>
    </div>

    <!-- Estado de carga -->
    <div *ngIf="loading" class="p-8 text-center">
      <mat-spinner diameter="40" class="mx-auto mb-4"></mat-spinner>
      <p class="text-gray-600 dark:text-gray-400">Cargando leads pendientes...</p>
    </div>

    <!-- Estado vacío -->
    <div *ngIf="!loading && pendingLeads.length === 0" class="p-8 text-center">
      <mat-icon class="text-6xl text-gray-400 mb-4">check_circle</mat-icon>
      <p class="text-gray-600 dark:text-gray-400">No hay leads pendientes de transcripción</p>
    </div>
  </div>
</div>

<!-- Footer del diálogo -->
<div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
  <div class="text-sm text-gray-600 dark:text-gray-400">
    {{ selectedLeads.length }} de {{ pendingLeads.length }} leads seleccionados
  </div>
  <div class="flex gap-3">
    <button mat-button (click)="close()" class="text-gray-600 hover:text-gray-800">
      Cerrar
    </button>
    <button
      mat-raised-button
      color="primary"
      (click)="processSelectedLeads()"
      [disabled]="processing || loading || pendingLeads.length === 0">
      <mat-icon class="mr-2">play_arrow</mat-icon>
      Procesar {{ selectedLeads.length > 0 ? selectedLeads.length : 'Todos' }}
    </button>
  </div>
</div>
